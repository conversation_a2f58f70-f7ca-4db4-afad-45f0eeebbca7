@element-font-path: 'fonts/element-icons';
@font-face {
  font-family: 'Raleway';
  src:  url('@/assets/fonts/Raleway.woff2') format('woff2'),
  url('@/assets/fonts/Raleway.ttf') format('truetype');
  font-weight: normal;
  font-style: normal
}
:root {
  --spacer-2xs: 0.25rem; // 4px
  --spacer-xs: 0.5rem; // 8px
  --spacer-sm: 1rem; // 16px
  --spacer-base: 1.5rem; // 24px
  --spacer-lg: 2rem; // 32px
  --spacer-xl: 2.5rem; // 40px
  --spacer-2xl: 5rem; // 80px
  --spacer-3xl: 10rem; // 160px

  --grey-100: #F7F7F7;
  --grey-500: #A1A1A1;
  --grey-600: #858585;
  --grey-700: #666666;
  --grey-800: #525252;
}
$mobile-max: 767px;
$tablet-min: 768px;
$tablet-max: 1023px;
$desktop-min: 1024px;
$desktop-l-min: 1200px;
$desktop-xl-min: 1440px;
$desktop-xxl-min: 1920px;
// Media mixins
@mixin for-mobile {
  @media (max-width: $desktop-min - 1px) {
    @content;
  }
}
@mixin for-desktop {
  @media (min-width: $desktop-min) {
    @content;
  }
}
@mixin fully-justify {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.desktop-only {
  @include for-desktop {
    display: block;
  }
  @include for-mobile {
    display: none !important;
  }
}
.smartphone-only {
  @include for-desktop {
    display: none !important;
  }
  @include for-mobile {
    display: block;
  }
}
.fully-justify {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.fully-justify-v {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.justify-left {
  display: flex;
  align-items: center;
}
.justify-right {
  display: flex;
  align-items: center;
  justify-content: right;
}
.main {
  max-width: 1000px;
  margin: auto;
  @include for-mobile {
    padding: 0 16px;
  }
}
.side-modal {
  display: flex;
  flex-direction: column;
  width: 600px;
  height: 100vh;
  overflow-y: auto;
  padding: 16px;
  background-color: #fff;
  @include for-mobile {
    width: 100vw;
    height: 80vh;
  }
}
.margin-auto {
  margin: 0 auto;
}
a.hover,
a:hover {
  text-decoration: underline;
  cursor: pointer;
}
.hidden {
  display: none !important;
}
.text-hover-underline:hover {
  text-decoration: underline;
  cursor: pointer;
}
