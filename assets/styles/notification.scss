/* ~/assets/css/main.css */
.notifications-container {
  position: fixed;
  top: 70px;
  right: 16px;
  z-index: 9999;
}

.notification {
  position: relative;
  width: 480px;
  max-width: 95vw;
  margin: 0 0 16px;
  padding: 14px 12px;
  border-radius: 8px;
  box-sizing: border-box;
  background-color: #ffffff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  transition:
          opacity 0.3s,
          transform 0.3s,
          left 0.3s,
          right 0.3s,
          top 0.4s,
          bottom 0.3s;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background-color: #E5F0FF;
  }
}

.notification.fade-out {
  transform: translateX(100%);
  opacity: 0;
}

.notification-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  & b {
    font-width: bolder;
  }
  & .close {
    cursor: pointer;
  }
}
.notification-content {
}

.notification-success {
  border: 1px solid #29CC29;
  background-color: #E6FFEF;
  &::before {
    background-color: #29CC29;
  }
  .notification-title b {
    color: #29CC29;
  }
}
.notification-error {
  border: 1px solid #F2460C;
  background-color: #FFECE6;
  &::before {
    background-color: #F2460C;
  }
  .notification-title b {
    color: #F2460C;
  }
}
.notification-info {
  border: 1px solid #1C77FF;
  background-color: #E5F0FF;
  &::before {
    background-color: #1C77FF;
  }
  .notification-title b {
    color: #1C77FF;
  }
}
.notification-warning {
  border: 1px solid #FF8C00;
  background-color: #FFF4E6;
  &::before {
    background-color: #FF8C00;
  }
  .notification-title b {
    color: #FF8C00;
  }
}
