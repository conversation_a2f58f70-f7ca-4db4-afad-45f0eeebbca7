export default defineNuxtPlugin((nuxtApp) => {
    const bar  = ref(null)
    nuxtApp.hook('app:mounted', (e) => {
        if (!bar.value) {
            const element = document.createElement("div")
            element.id = 'loading'
            element.className = 'progress-bar progress-bar-animate'
            document.body.appendChild(element)
            bar.value = document.getElementById('loading')
        }
    })
    nuxtApp.hook('page:start', (e) => {
        if (bar.value) {
            bar.value.style.display = 'block'
        }
    })
    nuxtApp.hook('page:finish', (e) => {
        console.log('page:finish')
        bar.value.style.display = 'none'
    })
    nuxtApp.hook('app:error', (e) => {
        console.log('app:error')
    })

})
