// ~/plugins/notifications.client.ts
import type { Plugin } from '#app'

// Defining the Notification Type
type NotificationType = 'success' | 'error' | 'info' | 'warning'

interface NotificationOptions {
    message: string
    type?: NotificationType
    timeout?: number
}

// Creating notification node
const createNotification = (options: NotificationOptions) => {
    const { message, type = 'info', timeout = 3000 } = options

    // create node
    const notification = document.createElement('div')
    notification.className = `notification notification-${type}`
    notification.innerHTML = `
    <div class="notification-title"><b>${type}</b><span class="close">X</span></div>
    <div class="notification-content">${message}</div>
  `

    // add to container
    const container = document.querySelector('#notifications-container') || createContainer()
    container.appendChild(notification)

    // remove
    setTimeout(() => {
        notification.classList.add('fade-out')
        setTimeout(() => notification.remove(), 3000)
    }, timeout)

    // Click to remove
    notification.addEventListener('click', () => notification.remove())
}

// Create the container if it doesn't exist
const createContainer = () => {
    const container = document.createElement('div')
    container.id = 'notifications-container'
    container.className = 'notifications-container'
    document.body.appendChild(container)
    return container
}

// Plugin Installation Methods
const plugin: Plugin = {
    install: (app) => {
        // Add global methods
        app.config.globalProperties.$notify = createNotification

        // Optional: Provides Composition API support
        app.provide('notify', createNotification)
    }
}

export default defineNuxtPlugin((nuxtApp) => {
    nuxtApp.vueApp.use(plugin)
})
