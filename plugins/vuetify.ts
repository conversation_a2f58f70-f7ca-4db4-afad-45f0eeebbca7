import { createVuetify } from 'vuetify'
import * as components from 'vuetify/components'
import * as directives from 'vuetify/directives'
import { mdi } from 'vuetify/iconsets/mdi'

export default defineNuxtPlugin(nuxtApp => {
    const vuetify = createVuetify({
        icons: {
            defaultSet: 'mdi',
            sets: { mdi }
        },
        theme: {
            defaultTheme: 'light'
        },
        components,
        directives,
    })

    nuxtApp.vueApp.use(vuetify)
})
