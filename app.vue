<template>
  <div id="app">
    <AppHeader v-if="!route.name.startsWith('index_')" :type="simpleMode ? 'simple' : 'normal'" @open="handleOpen" />
    <HeaderMenu  v-if="!(simpleMode || route.name.startsWith('index_'))" />
    <AnnounceBar v-if="!(simpleMode || route.name.startsWith('index_'))" />
    <NuxtPage @open="handleOpen" />
    <SubscribeBox v-if="!(simpleMode || isAccount)" />
    <AppFooter @open="handleOpen"/>

    <!--wishlist-->
    <WishlistSidebar v-model="showWishlist" />
    <!--cart-->
    <CartSidebar v-model="showCart" />
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const showCart = ref(false)
const showWishlist = ref(false)
const isAccount = computed(() => {
  const regex = /^(my-account)/;

  return regex.test(route.name)
})
const simpleMode = computed(() => {
  const regex = /^(signin|signup)/;

  return regex.test(route.name)
})
const handleOpen = (type) => {
  if (type ==  'cart')  {
    showCart.value = true
  } else if (type == 'wishlist') {
    showWishlist.value = true
  }
}
</script>
<style>
.fz-10 {
  font-size: 10px !important;
}
.fz-11 {
  font-size: 11px !important;
}

.fz-12 {
  font-size: 12px !important;
}

.fz-13 {
  font-size: 13px !important;
}

.fz-14 {
  font-size: 14px !important;
}

.fz-16 {
  font-size: 16px !important;
}

.fz-18 {
  font-size: 18px !important;
}

.fz-20 {
  font-size: 20px !important;
}
.fz-24 {
  font-size: 24px !important;
}
.fz-26 {
  font-size: 26px !important;
}
.fz-28 {
  font-size: 28px !important;
}
.fz-32 {
  font-size: 32px !important;
}
.progress-bar {
  width: 100%;
  height: 5px;
  position: fixed;
  top: 0px;
  z-index: 10;
  background-color: gray;
  animation: progress-animation 3s infinite;
}

@keyframes progress-animation {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}
</style>
