const http = useApi()

export default function useOrder() {
    let loading  = ref(false)
    const orders = ref([])
    const order = ref({})
    const load = (options) => {
        loading.value = true
        return http.get(`/api/v3/storefront/account/orders`).then(data => {
            orders.value = data
            return data
        })
    }

    const getOrder = (id) => {
        return http.get(`/api/v3/storefront/account/orders/${id}`).then(data => {
            order.value = data
            return data
        })
    }

    return {
        loading,
        orders,
        order,
        load,
        getOrder
    }
};
