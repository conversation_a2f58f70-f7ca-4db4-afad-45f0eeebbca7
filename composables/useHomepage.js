const http = useApi()

export default function useHomepage() {
    let loading  = ref(false)
    const homepageData = ref({})
    const load = async (options) => {
        loading.value = true
        return http.get(`/api/v3/storefront/pages/home`, options).then(data => {
            homepageData.value = data
            loading.value = false
            return data
        }).catch(error => {
            console.error('Homepage API error:', error)
            // Set default empty data if API fails
            homepageData.value = {}
            loading.value = false
            return {}
        })
    }

    return {
        loading,
        homepageData: computed(() => homepageData.value),
        load
    }
};
