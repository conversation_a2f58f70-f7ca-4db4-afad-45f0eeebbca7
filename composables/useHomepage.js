const http = useApi()

export default function useHomepage() {
    let loading  = ref(false)
    const homepageData = ref({})
    const load = async (options) => {
        loading.value = true
        return http.get(`/api/v3/storefront/pages/home`, options).then(data => {
            homepageData.value = data
            return data
        })
    }

    return {
        loading,
        homepageData: computed(() => homepageData.value),
        load
    }
};
