
export default function useApi() {

    const fetch = (url, options = {}) => {
        const { targetUrl, apiBaseUrl, env } = useRuntimeConfig().public;

        return new Promise(async (resolve, reject) => {
            // Check if we're on the client side before accessing localStorage
            const token = process.client ? localStorage.getItem('token') : null;
            console.log('fetch', options)
            $fetch(url, {
                ...options,
                // baseURL: targetUrl,
                server: !!options.client,
                credentials: 'include',
                initialCache: false,
                onRequest({ request, options }) {
                    // Check if we're on the client side before accessing localStorage
                    const token = process.client ? localStorage.getItem('token') : null;
                    console.log('useAPI', token)
                    if (token) {
                        options.headers.set('Authorization', token)
                    }
                },
                onResponse(res) {
                    const response = res.response
                    if (response.status == '200') {
                        console.log('res.response', res.response)
                        const headers = response.headers
                        const page = headers.get('x-page')
                        const totalPage = headers.get('x-total-pages')
                        const totalCount = headers.get('x-total-count')
                        console.log('headers', headers, 'page', res)

                        if (page || totalPage || totalCount) {
                            resolve({
                                page: parseInt(page),
                                totalPage,
                                totalCount,
                                data: response._data
                            })
                        } else {
                            resolve(response._data)
                        }
                    } else {
                        reject(response._data)
                    }
                },
                onRequestError({ error }) {
                    reject(error)
                }
            }, { immediate: true });
        });
    }

    const get = async (url, params = {}) => {
        return fetch(url, { method: 'get', params })
    }
    const post = async (url, params) => {
        return fetch(url, { method: 'post', body: params})
    }
    const patch = async (url, params) => {
        return fetch(url, { method: 'patch', params})
    }
    const del = async (url, id) => {
        return fetch(url, { method: 'delete', params: { id }})
    }

    return {
        fetch,
        get,
        post,
        patch,
        del
    }
};
