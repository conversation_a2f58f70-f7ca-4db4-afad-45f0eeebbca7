import Cookies from 'js-cookie'
const http = useApi()

export default function useUser() {
    const logIn = ({ username, password }) => {
        return http.post('/api/v3/storefront/account/sign_in', {
            username: username.toLowerCase(),
            password,
            grant_type: 'password'
        }).then(res => {
            if (res) {
                const token = `${res.token_type} ${res.access_token}`
                localStorage.setItem('token', token);
            }
        })
    }
    const logOut = () => {
        return http.post('/api/v3/storefront/account/logout').then(() => {
            localStorage.removeItem('token');
        })
    }
    const getAccount = () => {
        return http.get('/api/v3/storefront/account')
    }
    const resetPwd = (params) => {
        return http.post('/api/v3/storefront/account/reset_password', params).then(res => {
            console.log(res)
        })
    }
    const sendCode = (params) => {
        return http.post('/api/v3/email_confirmation/send_code', params)
    }
    const resendCode = (params) => {
        return http.post('/api/v3/email_confirmation/resend_code', params)
    }
    const verifyCode = (params) => {
        return http.post('/api/v3/email_confirmation/verify_code', params)
    }
    const signUp = (params) => {
        return http.post('/api/v3/storefront/account/sign_up', {
            params
        })
    }

    return {
        logIn,
        logOut,
        sendCode,
        signUp,
        resendCode,
        verifyCode,
        resetPwd,
        getAccount
    }
}
