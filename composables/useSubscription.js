const http = useApi()

export default function useSubscription() {
    let loading  = ref(false)
    const subscriptions = ref([])
    const subscription = ref({})
    const load = (options) => {
        loading.value = true
        return http.get(`/api/v3/storefront/subscriptions`).then(data => {
            subscriptions.value = data
            return data
        })
    }

    const getSubscribe = (id) => {
        return http.get(`/api/v3/storefront/subscriptions/${id}`).then(data => {
            subscription.value = data
            return data
        })
    }

    return {
        loading,
        subscriptions,
        subscription,
        load,
        getSubscribe
    }
};
