import { defineStore } from 'pinia'
import { ref } from 'vue'
const http = useApi()

export default function useBlog() {
    let loading = ref(false)
    const page = ref(1);
    const pageSize = ref(12);
    const totalPage = ref(1);
    const totalCount = ref(0);
    const blogs = ref([]);
    const blogCategories = ref([]);

    const getBlogCategories = () => {
        loading.value = true

        return http.get('/api/v3/storefront/blog_categories').then(res => {
            if (res.page)  {
                blogCategories.value = res.data
            } else {
                blogCategories.value = res
            }
            loading.value = false
            console.log(blogCategories.value)
            return blogCategories.value
        })
    };

    const getBlogs = (blog_category_id) => {
        loading.value = true

        return http.get('/api/v3/storefront/blog_posts', {
            page: page.value,
            per_page: pageSize.value,
            'filter[blog_category_id]': blog_category_id
        }).then(res => {
            if (res.page)  {
                page.value = res.page
                totalPage.value = res.totalPage
                totalCount.value = res.totalCount
                blogs.value = res.data
            } else {
                blogs.value = res
            }
            loading.value = false
            console.log(blogs.value)
            return blogs.value
        })
    };

    const getBlog = (id) => {
        return http.get(`/api/v3/storefront/blog_posts/${id}`).then(data => {
            return data
        })
    };

    const updateBlogLike = () => {
        return http.patch(`/api/v3/storefront/blog_posts/${id}/like`).then(data => {
            return data
        })
    }

    const changePageSize = (size) => {
        console.log('chnagepagesize', size)
        pageSize.value = size
        getBlogs()
    };

    const changePage = (index) => {
        page.value = index
        getBlogs()
    };

    return {
        loading,
        page,
        pageSize,
        totalPage,
        totalCount,
        blogs,
        getBlogCategories,
        getBlogs,
        getBlog,
        changePageSize,
        changePage
    }
};
