import { defineStore } from 'pinia'
import { ref } from 'vue'
const http = useApi()

export default function useProduct() {
    let loading = ref(false)
    const page = ref(1);
    const pageSize = ref(20);
    const totalPage = ref(1);
    const totalCount = ref(0);
    const filters = ref({
        sort: ''
    })
    const products = ref([]);

    const initItem = (data) => {
        return Object.assign(data, {
            default_variant: data.selected_variants.find(item => item.id == data.default_variant_id)
        })
    }
    const initData = (data) => {
        return data.map(item => initItem(item))
    }

    const getProducts = (params) => {
        loading.value = true
        let _params = {}
        if (params?.name) {
            Object.assign(_params, {
                'filter[name]': params.name
            })
        } else {
            Object.assign(_params, {
                page: page.value,
                per_page: pageSize.value,
                sort: filters.value.sort
            })
        }

        return http.get('/api/v3/storefront/listings', _params).then(res => {
            if (res.page)  {
                page.value = res.page
                totalPage.value = res.totalPage
                totalCount.value = res.totalCount
                products.value = initData(res.data)
            } else {
                products.value = initData(res)
            }
            loading.value = false
            return products.value
        })
    };

    const getOptions = () => {
        return http.get(`/api/v2/storefront/option_types`).then(data => {
            console.log(data)
        })
    }

    const getProduct = (id) => {
        return http.get(`/api/v3/storefront/listings/${id}`).then(data => {
            return initItem(data)
        })
    };

    const getFeaturedProducts = () => {
        return http.get(`/api/v3/storefront/featured_products`).then(res => {
            return initData(res)
        })
    };

    const changePageSize = (size) => {
        pageSize.value = size
        getProducts()
    };

    const changePage = (index) => {
        page.value = index
        getProducts()
    };

    const changeFilters = (data) => {
        Object.assign(filters.value, data)
        getProducts()
    }

    return {
        loading,
        page,
        pageSize,
        totalPage,
        totalCount,
        products,
        filters,
        getOptions,
        getProducts,
        getProduct,
        getFeaturedProducts,
        changeFilters,
        changePageSize,
        changePage
    }
};
