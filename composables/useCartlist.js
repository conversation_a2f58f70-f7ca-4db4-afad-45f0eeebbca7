import { defineStore } from 'pinia'
import { ref } from 'vue'
const http = useApi()

export default function useCartlist() {
    let loading = ref(false)
    const list = ref([])

    const getList = () => {
        loading.value = true

        return http.get('/api/v3/storefront/cart').then(res => {
            list.value = res.data
            loading.value = false
            return list.value
        })
    };

    const addItem = (params) => {
        loading.value = true
        const token = process.client ? localStorage.getItem('token') : null;
        return http.post(`/api/v3/storefront/cart/add_item`, params).then(res => {
            list.value = res.data
            loading.value = false
            return list.value
        })
    };

    const removeItem = (params) => {
        loading.value = true
        const token = process.client ? localStorage.getItem('token') : null;
        return http.post(`/api/v3/storefront/cart/add_item`, params).then(res => {
            list.value = res.data
            loading.value = false
            return list.value
        })
    };

    return {
        loading,
        list,
        getList,
        addItem,
        removeItem
    }
};
