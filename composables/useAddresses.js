const http = useApi()

export default function useAddresses() {
    let loading  = ref(false)
    const addresses = ref([])
    const load = (options) => {
        loading.value = true
        return http.get(`/api/v3/storefront/account/addresses`).then(data => {
            addresses.value = data
            return data
        })
    }
    const update = (params) => {
        console.log(params.id)
        return fetch(`/api/v3/storefront/account/addresses`, {
            method: params.id ? 'PATCH' : 'POST',
            params
        })
    }
    const remove = (id) => {
        return http.del(`/api/v3/storefront/account/addresses`, id)
    }

    return {
        loading,
        addresses,
        load,
        update,
        remove
    }
};
