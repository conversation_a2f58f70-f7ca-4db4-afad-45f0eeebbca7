import { defineStore } from 'pinia'
import { ref } from 'vue'
const http = useApi()

export default function useWishlist() {
    let loading = ref(false)
    const list = ref([])
    const wishId = ref(1)

    const getList = () => {
        loading.value = true

        return http.get('/api/v3/storefront/wishlists/default').then(res => {
            list.value = res.wished_items
            wishId.value = res.id
            loading.value = false
            return list.value
        })
    };

    const addItem = (params) => {
        loading.value = true
        return http.post(`/api/v3/storefront/wishlists/${wishId.value}/add_item`, params).then(res => {
            list.value = res.wished_items
            loading.value = false
            return list.value
        })
    };

    const removeItem = (id) => {
        loading.value = true
        return http.delete(`/api/v3/storefront/wishlists/${wishId.value}/remove_item/${id}`).then(res => {
            list.value = res.wished_items
            loading.value = false
            return list.value
        })
    };

    return {
        loading,
        list,
        getList,
        addItem
    }
};
