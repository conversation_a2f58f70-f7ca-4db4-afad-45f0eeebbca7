.template:
  stage: deploy
  needs:
    - build
  before_script:
    - docker login -u "$CI_REGISTRY_PULL_USER" -p "$CI_REGISTRY_PULL_PASSWORD" $CI_REGISTRY_PULL
  script:
    - chown -R gitlab-runner:gitlab-runner /home/<USER>
    - !-1 2>output1
    - source variables
    - docker pull ${IMAGE_PULL}@${IMAGE_HASH}
    - docker rm -f ${CI_PROJECT_NAME}
    - |
      docker run -d --name ${CI_PROJECT_NAME} \
        --restart unless-stopped \
        --net=host \
        -p 5000:5000 \
        -e PORT=${NUXT_SERVER_PORT} \
        ${IMAGE_PULL}@${IMAGE_HASH}
  artifacts:
    expire_in: 3 mos
    paths:
      - output1


.dev_deploy:
  extends: .template
  when: manual

.prod_deploy:
  extends: .template
  rules:
    - if: $CI_COMMIT_TAG =~ /^v.*$/
      when: manual
    - when: never


# prod server
.market1.axel.market:
  extends: .prod_deploy
  variables:
    SERVER_URL: https://axel.market
  tags:
    - market1r3

cipher.axel.network:
  extends: .dev_deploy
  variables:
    SERVER_URL: https://cipher.axel.network
  tags:
    - cipher_deploy

.cipher2.zencoo.com:
  extends: .dev_deploy
  variables:
    SERVER_URL: https://cipher2.zencoo.com
  tags:
    - cipher2_deploy

.dev-market-2.axel.network:
  extends: .dev_deploy
  variables:
    SERVER_URL: https://dev-market-2.axel.network
  tags:
    - dev_market_2_deploy

.dev-market-3.axel.network:
  extends: .dev_deploy
  variables:
    SERVER_URL: https://dev-market-3.axel.network
  tags:
    - dev_market_3_deploy
