build:
  stage: build
  image:
    name: ${CI_REGISTRY_PULL}/aws-cli-docker-cli:renew
    entrypoint: [""]
  services:
    - docker:dind
  variables:
    DOCKER_HOST: tcp://docker:2375
  before_script:
    - docker login -u "$CI_REGISTRY_PUSH_USER" -p "$CI_REGISTRY_PUSH_PASSWORD" $CI_REGISTRY_PUSH
    - docker login -u "$CI_REGISTRY_PULL_USER" -p "$CI_REGISTRY_PULL_PASSWORD" $CI_REGISTRY_PULL
  script:
    - docker pull $IMAGE_PULL || true
    - docker build --cache-from $IMAGE_PULL --pull --rm -t $IMAGE_PUSH
      --build-arg COMMIT_SHA="${CI_COMMIT_SHA}"
      --build-arg COMMIT_REF_NAME="${CI_COMMIT_REF_NAME}"
      --build-arg COMMIT_MESSAGE="${CI_COMMIT_MESSAGE}"
      --build-arg PROJECT_URL="${CI_PROJECT_URL}"
      --build-arg NPM_REGISTRY="$NPM_REGISTRY"
      --build-arg REGISTRY_PULL_USER="$CI_REGISTRY_PULL_USER"
      --build-arg DEPLOY_USER="${GITLAB_USER_NAME}"
      --build-arg COMMIT_DATE="$(date '+%Y-%m-%dT%H:%M:%S.000Z')"
      --build-arg REGISTRY_PULL_PASSWORD="$CI_REGISTRY_PULL_PASSWORD" .
    - export IMAGE_HASH=`docker push $IMAGE_PUSH | tail -1 | grep digest | cut -d ' ' -f 3`
    - echo "export IMAGE_HASH="${IMAGE_HASH}
    - echo "export IMAGE_HASH="${IMAGE_HASH} > variables
    ## push AWS ECR
    - export IMAGE_AWS_ECR=${AWS_ECR}/${CI_PROJECT_NAME}:${CI_COMMIT_REF_NAME}
    - echo "Push to AWS ECR the image ${IMAGE_AWS_ECR}"
    - docker login --username AWS -p $(aws ecr get-login-password) ${AWS_ECR}
    - docker tag $IMAGE_PUSH ${IMAGE_AWS_ECR}
    - docker push ${IMAGE_AWS_ECR}

  artifacts:
    expire_in: 3 mos
    paths:
      - variables

