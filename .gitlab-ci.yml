image: docker:latest

services:
  - docker:dind

stages:
  - analysis
  - build
  - tests
  - deploy_mirors  
  - deploy
  - "deploy promote"
  - "deploy fail"

variables:
  IMAGE_PUSH: ${CI_REGISTRY_PUSH}/${CI_PROJECT_NAME}:${CI_COMMIT_REF_NAME}
  IMAGE_PULL: ${CI_REGISTRY_PULL}/${CI_PROJECT_NAME}:${CI_COMMIT_REF_NAME}
  IMAGE_TAG: ${CI_COMMIT_REF_NAME}
  DOCKER_REGISTRY: ${CI_REGISTRY_PULL}
  DOCKER_REGISTRY_PULL_USER: ${CI_REGISTRY_PULL_USER}
  DOCKER_REGISTRY_PULL_PASSWORD: ${CI_REGISTRY_PULL_PASSWORD}
  SYSLOG_ADDRESS_CI: ${SYSLOG_ADDRESS}
  AWS_ECR: 674880015386.dkr.ecr.eu-west-2.amazonaws.com
  AWS_ACCESS_KEY_ID: ${AWS_ECR_ACCESS_KEY_ID}
  AWS_SECRET_ACCESS_KEY: ${AWS_ECR_ACCESS_KEY_SECRET}
  AWS_DEFAULT_REGION: eu-west-2
  NUXT_SERVER_PORT: ${NUXT_SERVER_PORT}
  FORCE_PULL: "false"

include:
  - local: '.gitlab/stages/*.yml'
