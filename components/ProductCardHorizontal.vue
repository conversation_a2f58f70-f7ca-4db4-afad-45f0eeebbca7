<template>
  <v-hover
      v-slot="{ isHovering, props }"
      open-delay="200"
  >
    <v-card class="product-card-horizontal"
        :class="['ma-4', props.class]"
        :elevation="isHovering ? 10 : 0"
        min-height="200"
        v-bind="props"
    >
      <div class="d-flex pa-4">
        <div class="product-card-horizontal__image">
          <v-img
              :width="210"
              :aspect-ratio="21/28"
              src="https://cdn.vuetifyjs.com/images/parallax/material.jpg"
          ></v-img>
        </div>
        <div class="product-card-horizontal__detail">
          <p class="text-decoration-underline">9-11-2024-variants9-11-2024-variants</p>
          <div class="product-card-horizontal__description">fdsafdsa</div>
          <div class="product-card-horizontal__configuration">
             <div class="desktop-only property">
               <span class="property__name">Color</span>
               <span class="property__value">Sunshine Yellow</span>
             </div>
          </div>
        </div>
        <div class="product-card-horizontal__actions">
          <div class="text-h6 text-right pb-4">$11.00</div>
          <div class="pt-4">
            <v-icon icon="mdi-heart-outline"></v-icon>
            <b class="text-decoration-underline ml-2">{{ $t('Save for later') }}</b>
          </div>
          <v-spacer />
          <div class="product-card-horizontal__add-to-cart">
            <v-number-input
                control-variant="split"
            ></v-number-input>
            <v-text-field class="mr-4" rounded="0" width="120" height="52" prepend-inner-icon="mdi-minus" append-inner-icon="mdi-plus" variant="outlined" />
            <v-btn  color="green" variant="elevated" height="56" rounded="0">{{ !addCartDisabled ? $t('Add to cart') : $t('Out of stock') }}</v-btn>
          </div>
        </div>
      </div>
    </v-card>
  </v-hover>
</template>

<script setup>

const props = defineProps(['data', 'class'])
const addCartDisabled = false;

</script>

<style lang="scss" scoped>
.product-card-horizontal {
  width: 100%;
  &__image {
    flex: 0 0 150px;
    width: 140px;
    height: 200px;
    background: #ccc;
  }
  &__detail {
    flex: 1;
    padding: 0 16px;
  }
  &__description {
    display: flex;
    align-items: center;
    padding: 16px 0px;
    min-height: 80px;
  }
  &__configuration {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-end;
    margin-top: 48px;

    .property__name, .property__value {
      text-transform: capitalize;
    }
    .property__name:after {
      content: ":";
    }
  }
  &__actions {
    display: flex;
    flex-direction: column;
    width: 300px;
    align-items: flex-end;
  }
  &__add-to-cart {
    display: flex;
  }
}
</style>
