<template>
  <v-lazy :class="props.class">
    <v-card
        elevation="0"
        border="0"
        height="400"
    >
      <v-card-item class="position-relative ma-2 pa-0">
        <v-img
            class="product-image "
            :width="240"
            :aspect-ratio="24/31"
            :src="getImgUrl"
        ></v-img>
        <v-btn icon="mdi-heart-outline" variant="text" class="product-wish bg-grey-lighten-5" @click="handleWish"></v-btn>
        <v-btn variant="flat" class="product-cart" color="black" rounded="0" block @click="handleCart">Add to cart</v-btn>
        <v-btn variant="flat" class="product-view" rounded="0" block @click="handleView"> + QUICK VIEW</v-btn>
      </v-card-item>
      <v-card-item>
        <p class="product-title" @click="gotoProduct(props.data?.id)">{{props.data?.title}}</p>
      </v-card-item>
      <v-card-text>
        <ProductPrice :data="props.data?.default_variant" />
      </v-card-text>
    </v-card>
  </v-lazy>
</template>

<script setup>
import {ref, computed} from 'vue'

import { useRouter } from 'vue-router'
const config = useRuntimeConfig()
const router = useRouter()
const emit = defineEmits(['view'])
const props = defineProps(['class', 'data'])

const getImgUrl = computed(() => {
  const vart = props.data.selected_variants.find(item => item.id == props.data.default_variant_id)
  return vart?.images?.length > 0 ?
      config.public.targetUrl + vart.images[0]['original_url'] :
      config.app.buildAssetsDir + 'assets/images/default.jpg'
})

const handleView = () => {
  emit('view', props.data)
}
const handleCart = () => {
  emit('cart', props.data)
}
const handleWish = () => {
  emit('wish', props.data)
}
const gotoProduct = (id) => {
  console.log(id, 'goto product')
  router.push(`/product/${id}`)
}
</script>
<style lang="scss" scoped>
.product-cart {
  position: absolute;
  bottom: 66px;
  height: 48px;
  display: none;
}
.product-view {
  position: absolute;
  bottom: 10px;
  height: 48px;
  background: radial-gradient(circle, white, transparent);
  display: none;
}
.product-wish {
  position: absolute;
  top: 10px;
  right: 10px;
  display: none;
}
.product-title {
  display: -webkit-box;
  text-overflow: ellipsis;
  word-break: break-word;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  cursor: pointer;
  &:hover {
    text-decoration: underline;
  }
}
.v-card:hover {
   .product-image {
     background: var(--grey-100);
     transform: scale(2.5);
     transition: transform 0.5s ease;
   }
  .product-cart,.product-view,.product-wish {
    display: block;
  }
}
</style>
