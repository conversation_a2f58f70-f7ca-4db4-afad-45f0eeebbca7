<template>
  <div class="gallery">
    <ul class="gallery__slides" :style="slideStyle">
      <li class="gallery__slide" :style="'grid-area: item_' + index" v-for="(item, index) in props.data">
        <img
            loading="lazy"
            :src="config.public.targetUrl + item.original_url"
            width="100%"
        />
      </li>
    </ul>
    <v-carousel>
      <v-carousel-item v-for="(item, index) in props.data"
                       :src="config.public.targetUrl + item.original_url"
          cover
      ></v-carousel-item>
    </v-carousel>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

const config = useRuntimeConfig()
const props = defineProps({
  /**
   * Toogle for image zoom or overlap the stage
   */
  enableZoom: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Array,
    default: []
  }
})

const activeImg = ref('https://cdn.vuetifyjs.com/images/cards/sunshine.jpg');
const pictureSelected = ref();
const slideStyle = computed(() => {
  const group = parseInt(props.data.length / 3)
  const remainder = props.data.length % 3
  console.log(group + '______' + remainder)
  let str = ''
  for(let i = 0; i < group; i++) {
    str += `"item_${3*i} item_${3*i}" `
    str += `"item_${3*i+1} item_${3*i+2}" `
  }
  for(let i = 0; i < remainder; i++) {
    str += `"item_${3*group+i} item_${3*group+i}" `
  }

  console.log(str)
  return 'grid-template-areas:' + str;
})
const go = (url) => {
  pictureSelected.value = picture;
}
const moveZoom = ($event, index) => {
  if (this.enableZoom) {
    this.eventHover = $event;
    if (this.outsideZoom) {
      this.positionStatic = this.positionObject(index);
      this.$refs.imgZoom.$el.children[0].style.cssText =
          "top: 0; transform: scale(2);";
      this.$refs.imgZoom.$el.children[0].style.transformOrigin = `${
          $event.clientX - this.positionStatic.x
      }px ${$event.clientY - this.positionStatic.y}px`;
    } else {
      this.positionStatic = this.positionObject(index);
      this.$refs.sfGalleryBigImage[index].$el.children[0].style.cssText =
          "top: 0; transform: scale(2);";
      this.$refs.sfGalleryBigImage[
          index
          ].$el.children[0].style.transformOrigin = `${
          $event.clientX - this.positionStatic.x
      }px ${$event.clientY - this.positionStatic.y}px`;
    }
  }
}
</script>

<style lang="scss" scoped>
@use '~/assets/styles/default' as *;
.gallery {
  width: 500px;
  .v-carousel {
    display: none;
  }
  &__slides {
    list-style: none !important;
    display: grid !important;
    grid-gap: 10px;
  }
  &__slide {
    max-width: 100%;
  }
  @include for-mobile {
    width: 100%;
    .v-carousel {
      display: block !important;
    }
    &__slides {
      display: none !important;
    }
  }
}

</style>
