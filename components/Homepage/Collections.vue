<template>
  <div
    class="homepage__collections collections"
    :style="{
      color: props.data.text_color,
      backgroundColor: props.data.bg_color
    }"
  >
    <div class="text-h4 mb-4">{{ props.data.header || 'Collections Title' }}</div>
    <div class="collections__list">
      <div class="collection-item" v-for="item in props.data.collections">
        <div class="collection-img" v-if="item.image_url">
          <img :src="targetUrl + item.image_url" alt="">
        </div>
        <div v-else class="collection-img bg-grey-lighten-1"></div>
        <div class="collection-desc">
          <span class="name">{{ item.button_label || 'Shop collection' }}</span>
          <span class="arrow-icon">
            <svg t="1741327640558" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4048" width="20" height="20"><path d="M96 540.8l755.2 0-230.4 236.8c-12.8 12.8-12.8 32 0 44.8 12.8 12.8 32 12.8 44.8 0l284.8-291.2c0 0 0-3.2 3.2-3.2 3.2-3.2 3.2-3.2 3.2-6.4 0-3.2 3.2-6.4 3.2-9.6 0 0 0 0 0-3.2 0 0 0 0 0 0 0-3.2 0-9.6-3.2-12.8-3.2-3.2-3.2-6.4-6.4-9.6l-288-288C656 192 649.6 188.8 640 188.8S624 192 617.6 198.4c-12.8 12.8-12.8 32 0 44.8l233.6 233.6L96 476.8c-19.2 0-32 12.8-32 32S76.8 540.8 96 540.8z" p-id="4049" :fill="props.data.collections_text_color || '#000'"></path></svg>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
// const { targetUrl } = useRuntimeConfig().public;
const targetUrl = 'https://newmarket.viacube.com'
const props = defineProps({
  data: {}
})
</script>
<style lang="scss" scoped>
@use '~/assets/styles/default' as *;
.collections {
  padding: 12px;

  @include for-mobile {
    padding: 16px;
  }

  & &__list {
    display: flex;
    margin-top: 15px;
    flex-wrap: wrap;

    .collection-item {
      width: 32%;
      display: flex;
      flex-direction: column;
      margin-bottom: 10px;

      &:not(:first-child) {
        margin-left: 2%;
      }

      .collection-img {
        width: 100%;
        height: 290px;
        background-color: #f5f5f5;
        border: none;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        @include for-mobile {
          height: 90vw;
        }
      }

      .collection-desc {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 10px;
        .arrow-icon {
          display: flex;
          cursor: pointer;
        }
      }


      @include for-mobile {
        width: 100%;
        margin-left: 0!important;
        &:not(:first-child) {
          margin-top: 15px;
        }
      }
    }
  }
}
</style>
