<template>
  <div
    class="homepage__columns columns"
    :style="{
      color: props.data.text_color,
      backgroundColor: props.data.bg_color
    }"
  >
    <div class="columns__list">
      <div class="column-item pa-4" v-for="item in props.data.columns">
        <div class="column-img mb-4" v-if="item.image_url">
          <img :src="targetUrl + item.image_url" alt="">
        </div>
        <div class="text-h6 mb-4 user-select-none text-hover-underline" v-if="item.header_visible" @click="handleJump(item)">
          {{ item.header || 'Column heading' }}
        </div>
        <div class="text-body-2" v-if="item.content_visible">
          {{ item.content || 'Combine text with an image to emphasise your chosen product, collection or blog post. Add details about availability, style or even leave a review.' }}
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { useRouter } from 'vue-router'

// const { targetUrl } = useRuntimeConfig().public;
const targetUrl = 'https://newmarket.viacube.com'
const props = defineProps({
  data: {}
})
const homepageData = ref(props.data)
const router = useRouter()

const handleJump = (item) => {
  if (!item.link) return false

  router.push(root.localePath(item.link))
}
</script>
<style lang="scss" scoped>
@use '~/assets/styles/default' as *;
.columns {
  padding: 12px;

  @include for-mobile {
    padding: 16px;
  }

  & &__list {
    display: flex;
    flex-wrap: wrap;

    .column-item {
      width: 32%;
      display: flex;
      flex-direction: column;
      margin-bottom: 10px;

      &:not(:first-child) {
        margin-left: 2%;
      }

      .column-img {
        width: 100%;
        height: 290px;
        background-color: #f5f5f5;
        border: none;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        @include for-mobile {
          height: 90vw;
        }
      }

      @include for-mobile {
        width: 100%;
        margin-left: 0!important;
        &:not(:first-child) {
          margin-top: 15px;
        }
      }
    }
  }
}
</style>
