<template>
  <button
    class="axel-button"
    :style="{
      backgroundColor: bgColor,
      color: color
    }"
    @click="handleJump"
  >
    <span>
      <slot></slot>
    </span>
  </button>
</template>
<script setup>
import { useRoute, useRouter } from 'vue-router'

const props = defineProps({
  bgColor: '#000',
  color: '#fff',
  link: ''
})

const router = useRouter()
const handleJump = () => {
  if (!props.link) return false

  router.push(root.localePath(props.link))
}
</script>
<style lang="scss" scoped>
.axel-button {
  all: unset;
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  font-size: inherit;
  font-family: inherit;
  color: inherit;
  cursor: pointer;
  outline: none;
  height: 42px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #333;
  > span {
    mix-blend-mode: difference;
    color: #fff;
    text-transform: capitalize;
  }
}
</style>
