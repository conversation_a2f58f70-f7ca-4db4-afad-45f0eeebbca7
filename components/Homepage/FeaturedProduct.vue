<template>
  <div
    class="homepage__featured-products featured-products"
    :style="{
      backgroundColor: props.data.bg_color,
      color: props.data.text_color
    }"
  >
    <div class="section-title" v-if="props.data.header_visible">{{ props.data.header }}</div>
    <div class="featured-products__products-list">
      <template v-for="(item, index) in props.data.collection">
        <div
          class="product-item"
          v-if="index < 3 || (index >= 3 && props.data.collection)"
          @click="handleJumptoProduct(item)"
        >
          <div class="product-img">
            <img v-if="getProductImage(item)" :src="targetUrl + getProductImage(item)" alt="">
          </div>
          <div class="product-name" :title="item.product.name">{{ item.product.name }}</div>
          <div class="product-price">{{ `$${item.price.toFixed(2)}` || '-' }}</div>
        </div>
      </template>
    </div>
  </div>
</template>
<script setup>
import { useRouter } from 'vue-router'
// const { targetUrl } = useRuntimeConfig().public;
const targetUrl = 'https://newmarket.viacube.com'
const props = defineProps({
  data: {
    type: Object,
    default() {
      return {}
    }
  }
})
const router = useRouter()

const getProductImage = (item) => {
  let imageUrlString = ''

  try {
    if (item.image_path) {
      item.image_path.some((img) => {
        if (typeof img == 'string') {
          imageUrlString = img

          return !!img
        }

        return img?.image_url_list.some(({ image_url }) => {
          imageUrlString = image_url
          return !!image_url
        })
      })
    }

    if (!imageUrlString) {
      if (typeof item.product.image_path == 'string') {
        return item.product.image_path
      }

      item.product.image_path.some((productImg) => {
        if (typeof productImg == 'string') {
          imageUrlString = productImg

          return !!img
        } else {
          const { image_url_list, imageUrlList } = productImg
          const imageList = image_url_list || imageUrlList
          return imageList.some(({ image_url, imageUrl }) => {
            imageUrlString = image_url || imageUrl
            return !!(image_url || imageUrl)
          })
        }
      })
    }
  } catch (e) {
    console.warn(e)
  }

  return imageUrlString
}

const handleJumptoProduct = (item) => {
  router.push(root.localePath(`/p/${item.product.id}/${item.id}/${item.product.slug}`))
}
</script>
<style lang="scss" scoped>
@use '~/assets/styles/default' as *;
.featured-products {
  padding: 12px;

  @include for-mobile {
    padding: 16px;
  }
  .section-title {
    font-size: 34px;
    font-weight: bold;
  }

  & &__products-list {
    display: flex;
    margin-top: 15px;
    flex-wrap: wrap;

    .product-item {
      width: 32%;
      display: flex;
      flex-direction: column;
      margin-bottom: 10px;
      cursor: pointer;

      &:not(:nth-child(3n+1)) {
        margin-left: 2%;
      }

      .product-img {
        width: 100%;
        height: 290px;
        background-color: #f5f5f5;
        border: none;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        @include for-mobile {
          height: 90vw;
        }
      }

      .product-name {
        margin-top: 10px;
        display: block;
        display: -webkit-box;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-word;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }

      .product-price {
        font-weight: bold;
        margin-top: 5px;
      }

      @include for-mobile {
        width: 100%;
        margin-left: 0!important;
      }
    }
  }
}
</style>
