<template>
  <div class="announce-bar" :style="{'background': props.data.bg_color, 'color': props.data.text_color}">
    <div class="announce-bar-content">
      <div v-if="props.data.left_content_visible" class="announce-bar-item text-hover-underline" @click="jumpto(props.data.left_link)">
        {{props.data.left_content || 'Announcement information'}}
      </div>
      <div v-if="props.data.middle_content_visible" class="announce-bar-item text-hover-underline" @click="jumpto(props.data.middle_link)">
        {{props.data.middle_content || 'Announcement information'}}
      </div>
      <div v-if="props.data.right_content_visible" class="announce-bar-item text-hover-underline" @click="jumpto(props.data.right_link)">
        {{props.data.right_content || 'Announcement information'}}
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()
const items = [
  'Free delivery when you spend $60',
  'Announcement information',
  'Announcement information'
];
const props = defineProps({
  data: {
    type: Object,
    default() {
      return {}
    }
  }
})
const jumpto = (url) => {
  url && router.push(url)
}
</script>

<style lang="scss">
.announce-bar {
  height: 40px;
  line-height: 40px;
  border-top: 1px solid var(--grey-800);
  border-bottom: 1px solid var(--grey-800);
  &-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 1000px;
    margin: 0 auto;
    text-align: center;
  }
}
</style>
