<template>
  <div class="homepage__image-banners image-banners">
    <template v-for="(item, index) in props.data.images">
      <div
        class="image-banners__item flex-d-column position-box"
        :class="item.content_position ? item.content_position.toLowerCase() : 'top-left'"
        :style="{
          backgroundImage: `url('${targetUrl + item.url}')`,
          color: item.text_color
        }"
        v-if="index == 0 || (index == 1 && props.data.second_visible)"
      >
        <div
          class="item-content"
          :class="item.content_alignment ? item.content_alignment.toLowerCase() : 'left'"
          :style="{
            color: item.text_color
          }"
        >
          <h2 class="text-h4 mb-4" v-if="item.header_visible">{{ item.header || 'Welcome to our shop' }}</h2>
          <AxelButton
            class="item-action mb-4"
            v-if="item.button_visible"
            :color="item.text_color"
            :bgColor="item.button_color"
            :link="item.button_link"
          >
            {{ item.button_label || 'Button' }}
          </AxelButton>
          <div class="text-body-1" v-if="item.content_visible">{{ item.content || 'Describe product or services selling or providing to customer' }}</div>
        </div>
      </div>
    </template>
  </div>
</template>
<script setup>
import AxelButton from './AxelButton.vue'
// const { targetUrl } = useRuntimeConfig().public;
const targetUrl = 'https://newmarket.viacube.com/'
const props = defineProps({
  data: {}
})
</script>
<style lang="scss" scoped>
@use '~/assets/styles/default' as *;
.image-banners {
  display: flex;
  min-height: 600px;
  & &__item {
    flex: 1;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;

    @include for-mobile {
      min-height: 50vw;
    }

    .item-content {
      padding: 24px;
      box-sizing: border-box;
    }

    .item-title {
      font-weight: 400;
      line-height: 1.2;
      color: inherit;
    }

    .item-action {
      margin-top: 20px;
      max-width: 200px;
    }

    .axel-button {
      display: inline;
    }

    .item-desc {
      margin-top: 12px;
    }

    .left {
      text-align: left;
    }

    .center {
      text-align: center;
    }

    .right {
      text-align: right;
    }
  }

  @include for-mobile {
    flex-direction: column;
    min-height: 193px;
  }
}
</style>
