<template>
  <div
    class="homepage__featured-article featured-article"
    :class="props.data.content_position ? props.data.content_position.toLowerCase() : 'left'"
    :style="{
      backgroundColor: props.data.bg_color,
      color: props.data.text_color
    }"
  >
    <div class="featured-article__img pa-4">
      <div class="img-box">
        <img :src="targetUrl + props.data.image_url" alt="">
      </div>
    </div>
    <div class="featured-article__desc pa-4">
      <div
        class="desc-box position-box flex-d-column"
        :class="props.data.content_alignment ? props.data.content_alignment.toLowerCase() : 'left'"
      >
        <h2 class="title mb-6" v-if="props.data.header_visible">{{ props.data.header || 'Talk about the value of your company\'s product or service' }}</h2>
        <p class="content mb-6" v-if="props.data.content_visible">{{ props.data.content || 'Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability' }}</p>
        <AxelButton
          :bgColor="props.data.button_color"
          :link="props.data.button_link"
          v-if="props.data.button_visible"
        >{{ props.data.button_label || 'Button Label' }}</AxelButton>
      </div>
    </div>
  </div>
</template>
<script setup>
import AxelButton from './AxelButton.vue';
// const { targetUrl } = useRuntimeConfig().public;
const targetUrl = 'https://newmarket.viacube.com/'
const props = defineProps({
  data: {}
})
const homepageData = ref(props.data)
</script>
<style lang="scss" scoped>
@use '~/assets/styles/default' as *;
.featured-article {
  display: flex;

  & &__img {
    flex: 0;
    min-width: 33%;
    height:250px;
    .img-box {
      width: 100%;
      height: 100%;
      background-color: #f7f7f7;
      img {
        object-fit: cover;
        width: inherit;
        height: inherit;
      }
    }
  }

  & &__desc {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    .title {
      text-align: 34px;
      font-weight: bold;
      line-height: 1.175;
    }
  }

  &.left {
    flex-direction: row;
  }

  &.right {
    flex-direction: row-reverse;
  }

  @include for-mobile {
    flex-direction: column!important;
    padding: 16px;

    & &__img {
      width: 100%;
      padding: 0;
      .img-box {
        height:250px;
      }
    }

    & &__desc {
      padding: 0;
      margin-top: 10px;
    }
  }
}
</style>
