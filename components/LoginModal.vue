<template>
  <v-dialog max-width="500" v-model="props.modelValue">
    <v-card>
      <v-card-title class="d-flex align-center">
        <span class="pl-2">{{props.title}}</span>
        <v-spacer />
        <v-btn icon="mdi-close" variant="text" @click="emit('update:modelValue', false)"></v-btn>
      </v-card-title>
      <slot></slot>
      <v-card-text>
        <v-form fast-fail @submit.prevent="login">
          <v-text-field
              v-model="formData.username"
              :rules="[rules.required]"
              prepend-inner-icon="mdi-at"
              class="mb-2"
              :label="$t('Your email')"
              variant="outlined"
              clearable
              active
              required
          ></v-text-field>

          <v-text-field
              v-model="formData.password"
              :rules="[rules.required]"
              prepend-inner-icon="mdi-lock-outline"
              :append-inner-icon="pwdType ? 'mdi-eye' : 'mdi-eye-off'"
              :type="pwdType ? 'text' : 'password'"
              @click:append-inner="pwdType = !pwdType"
              class="mb-2"
              :label="$t('Password')"
              variant="outlined"
              clearable
              active
              required
          ></v-text-field>

          <div class="fully-justify">
            <v-checkbox
                v-model="formData.remember_me"
                :label="$t('Remember me')"
            ></v-checkbox>
            <div class="text-decoration-underline">{{$t('Forgotten password?')}}</div>
          </div>

          <v-btn class="mt-2" color="black" size="large" rounded="0" type="submit" block>{{$t('Sign in')}}</v-btn>
        </v-form>
        <v-btn class="mt-8" color="#F7F7F7" size="large" rounded="0" variant="flat" block>{{$t('CONTINUE AS GUEST')}}</v-btn>
      </v-card-text>
    </v-card>
  </v-dialog>
</template>
<script setup>
import { ref, inject, computed, reactive, readonly } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: 'Login'
  }
})
const emit = defineEmits(['update:modelValue'])
const rules = readonly({
  required: (value) => {
    if (!value) {
      return 'This field is required'
    }
  }
})
const pwdType = ref(true)
const formData = reactive({
  username: '',
  password: '',
  remember_me: false
})

const login = async (evt) => {
  const { valid } = await evt.then()

  if (!valid) {
    return false
  }
  fetch
      .post('/api/spree/logIn', {
        ...formData
      }, {
        baseURL: '/'
      })
}
</script>

<style lang="scss" scoped>

</style>
