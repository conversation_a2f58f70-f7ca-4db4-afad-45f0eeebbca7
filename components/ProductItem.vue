<template>
  <div class="product-item">
    <img
        class="product-image "
        width="300"
        height="300"
        border="0"
        :aspect-ratio="1/1"
        :src="getImgUrl(props.data)"
    />
    <p class="py-2" @click="gotoProduct(props.data.id)">{{props.data.title}}</p>
    <h3>{{ props.data.default_variant?.listing_inventory?.display_price }}</h3>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()
const props = defineProps(['title', 'data'])

const getImgUrl = (item) => {
  console.log(item.product, 'item.product')
  return item.default_variant?.images?.length > 0 ? 'https://test5.axel.market/' + item.default_variant.images[0]['original_url'] :
   (item.product?.images?.length > 0 ? 'https://test5.axel.market/' + item.product.images[0]['original_url'] : '/_nuxt/assets/images/default.jpg')
}
const gotoProduct = (id) => {
  router.push({
    name: 'product',
    params: {
      id
    }
  })
}
</script>

<style lang="scss">
.product-item {
  display: flex;
  flex-direction: column;
  margin-right:2px;
  img {
    background: var(--grey-100);
    border: 0px;
  }
  &:last-child {
    margin-right: 0px;
  }
}
</style>
