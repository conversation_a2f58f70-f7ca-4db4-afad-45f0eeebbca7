<template>
  <div class="sf-alert justify-left">
    <v-icon :icon="props.icon" size="42" color="#1C77FF"/>
    <div class="ml-6">{{props.message}}</div>
  </div>
</template>

<script setup>
const props = defineProps({
  icon: {
    type: String,
    default: ''
  },
  message: {
    type: String,
    default: ''
  }
})
</script>

<style lang="scss">
.sf-alert {
  background: #F1F5FF;
  padding: 16px;
}
</style>
