<template>
  <v-menu class="mr-4">
    <template v-slot:activator="{ props }">
      <v-btn icon="" variant="text" v-bind="props">
        <span class="icon en" />
      </v-btn>
    </template>
    <v-list rounded="0" class="pa-0" v-model="locale">
      <v-list-subheader>{{$t('Change locale')}}</v-list-subheader>
      <v-list-item
          v-for="(item, i) in locales"
          :key="i"
          :value="item.code"
          :active="isLocaleSelected(item)"
          @click="switchLanguage(item.code)"
      >

        <template v-slot:prepend>
          <span class="icon mr-2" :class="item.code"/>
        </template>
        <v-list-item-title v-text="item.label"></v-list-item-title>
      </v-list-item>
      <v-list-item variant="tonal" class="text-center cursor-pointer">{{$t('Cancel')}}</v-list-item>
    </v-list>
  </v-menu>
<!--  <span class="text-capitalize cursor-pointer" @click="jumpToFreeShipping">{{$t('Free Shipping')}}</span>-->
</template>
<script setup>
import { useRouter } from 'vue-router'

const router = useRouter();
const { locale, locales, setLocale } = useI18n()

const switchLanguage = async (lang) => {
  await setLocale(lang);
}
const isLocaleSelected = ({ code }) => {
  return locale.value === code
}
const jumpToFreeShipping = () => {
  router.push({name: 'freeShipping'});
}
const handleLocale = (item) => {
  console.log(item)

}
</script>

<style lang="scss" scoped>
.icon {
  width: 20px;
  height: 20px;
  background-size: 20px 20px;
  background-position: center center;
  background-repeat: no-repeat;
}
.en {
  background-image: url('~/assets/images/langs/en.png');
}
.es {
  background-image: url('~/assets/images/langs/es.png');
}
.pt {
  background-image: url('~/assets/images/langs/pt.png');
}
</style>
