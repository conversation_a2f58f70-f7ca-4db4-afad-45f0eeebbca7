<template>
  <v-lazy>
    <div class="feature-products">
      <div class="title">{{props.title}}</div>
      <div class="content">
        <ProductItem :data="item" v-for="item in props.data"/>
      </div>
    </div>
  </v-lazy>
</template>

<script setup>
import ProductItem from "./ProductItem.vue";

const props = defineProps(['title', 'data'])

</script>

<style lang="scss" scoped>
.title {
  font-size: 32px;
  margin-top: 24px;
  margin-bottom: 12px;
}
.content {
  display: flex;
  flex-wrap: wrap;
}
.feature-products {
  margin: 0 auto;
}
</style>
