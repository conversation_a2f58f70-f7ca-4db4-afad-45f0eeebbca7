<template>
  <footer class="footer">
    <div class="footer-content pa-12">
      <div class="cols-3">
        <p class="pb-2" v-for="n in items">{{n.title}}</p>
      </div>
      <div class="cols-2">
        <p class="pb-2" v-for="n in ['About Us', 'Contact Us', 'Blog']">{{n}}</p>
      </div>
      <div class="cols-3 desktop-only">
        <div class="mt-8 d-flex">
          <client-only>
            <a
                v-for="item in socials"
                :href="item.link"
                class="mr-2"
                target="_blank">
              <img src="~/assets/images/footer/blank.svg" class="social-icon" :class="item.type" :alt="item.type"/>
            </a>
          </client-only>
        </div>
      </div>
      <div class="cols-4 desktop-only">
        <div class="footer-payment">
          <img
              src="~/assets/images/footer/payment.svg"
              alt="App store"
              width="240"
          />
        </div>
        <div class="footer-payment-items">
          <img
              class="mr-2"
              src="~/assets/images/footer/visa.svg"
              alt="App store"
              width="50"
          />
          <img
              class="mr-2"
              src="~/assets/images/footer/mastercard.svg"
              alt="App store"
              width="50"
          />
          <img
              class="mr-2"
              src="~/assets/images/footer/amex.svg"
              alt="App store"
              width="50"
          />
          <img
              class="mr-2"
              src="~/assets/images/footer/discover.svg"
              alt="App store"
              width="50"
          />
          <img
              src="~/assets/images/footer/paypal.svg"
              alt="App store"
              width="50"
          />
        </div>
      </div>
    </div>
    <div class="footer-menu smartphone-only">
      <v-btn variant="text" stacked  @click="handleMenu('home')">
        <v-icon icon="mdi-home"></v-icon>
        <span>{{$t('Home')}}</span>
      </v-btn>
      <v-btn variant="text" stacked @click="handleMenu('cart')">
        <v-icon icon="mdi-cart" class="hover"></v-icon>
        <span>{{$t('Cart')}}</span>
      </v-btn>
      <v-btn variant="text" stacked @click="handleMenu('account')">
        <v-icon>mdi-account-outline</v-icon>
        <span>{{$t('Account')}}</span>
      </v-btn>
      <v-btn variant="text" stacked @click="handleMenu('wishlist')">
        <v-icon>mdi-heart-outline</v-icon>
        <span>{{$t('Wishlist')}}</span>
      </v-btn>
    </div>
  </footer>
</template>
<script setup>
import { useRouter } from 'vue-router'

const router = useRouter()
const emit = defineEmits('open')
const items = [
  {
    title: 'Privacy Policy',
    value: 1,
  },
  {
    title: 'Terms and Conditions',
    value: 2,
  },
  {
    title: 'Return and Refund',
    value: 3,
  },  {
    title: 'Shipping and Delivery',
    value: 4,
  },
]
const socials = [{
  type: 'facebook',
  link: 'https://facebook.com/axel.axel.market'
},{
  type: 'instagram',
  link: 'https://instagram.com/shopaxelmarket/'
},{
  type: 'twitter',
  link: 'https://twitter.com/ShopAxelMarket?mx=2'
}]
const handleMenu = (type) => {
  if (type == 'home') {
    router.push('/')
  } else if (type  == 'account') {
    router.push('/my-account')
  } else {
    emit('open', type)
  }
}
</script>

<style lang="scss" scoped>
@use '~/assets/styles/default' as *;
.footer {
  color: white;
  background: #151414;
  @include for-mobile {
    padding-bottom: 72px;
  }
  &-content {
    display: flex;
    width: 1072px;
    margin: 0 auto;
    .cols-2 {
      flex: 0 0 20%
    }
    .cols-3 {
      flex: 0 0 30%
    }
    .cols-4 {
      flex: 0 0 40%
    }
    @include for-mobile {
      max-width: 100%;
      display: block;
      .cols-2,.cols-3,.cols-4 {
        margin-bottom: 24px;
      }
    }
  }

  &-menu {
    position: fixed;
    bottom: 0px;
    width: 100%;
    height: 72px;
    background: var(--grey-600);
    display: flex;
    justify-content: space-between;
    z-index: 1;
    .v-btn {
      flex: 0 0 25%;
      text-transform: capitalize;
    }
    .hover:before {
      position: absolute;
      top: -40px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 56px;
      height: 56px;
      background: #009936;
      border-radius: 28px;
    }
  }

  &-payment {
    padding-top: 30px;
    padding-bottom: 20px;
    justify-items: right;
  }
  &-payment-items {
    display: flex;
    align-items: flex-end;
  }
  .social-icon {
    width: 32px;
    height: 32px;
    background-repeat: no-repeat;
    background-position: center center;
  }
  .facebook {
    background: url('assets/images/footer/facebook.svg');
  }
  .instagram {
    background: url('assets/images/footer/instagram.svg');
  }
  .twitter {
    background: url('assets/images/footer/twitter.svg');
  }
}
</style>
