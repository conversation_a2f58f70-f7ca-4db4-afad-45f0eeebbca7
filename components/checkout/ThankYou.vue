<template>
  <div class="account-info" v-if="createAccFlag">
    <SfAlert
        class="mb-4"
        icon="mdi-account"
        message="Create an account with us and you can view your order status online. Your details are also saved for faster checkout in future." />
    <v-from>
      <v-text-field
          label="Full Name"
          prepend-inner-icon="mdi-account"
          clearable
      ></v-text-field>
      <v-row>
        <v-col>
          <v-text-field
              label="Email"
              prepend-inner-icon="mdi-at"
              clearable
          ></v-text-field>
        </v-col>
        <v-col>
          <v-text-field
              label="Phone number"
              prepend-inner-icon="mdi-lock-outline"
              clearable
          ></v-text-field>
        </v-col>
      </v-row>
      <v-text-field
          label="Create Password"
          prepend-inner-icon="mdi-cellphone"
          clearable
      ></v-text-field>

      <v-checkbox hide-details>
        <template v-slot:label>
          <b>Agree to Terms and conditions</b>
        </template>
      </v-checkbox>

      <v-checkbox hide-details>
        <template v-slot:label>
          <b>I want to receive newsletter and promotional materials</b>
        </template>
      </v-checkbox>

      <v-btn
          variant="elevated"
          height="52"
          rounded="0"
          color="black"
          block
          type="submit"
          @click="createAccFlag = false"
      >CREATE ACCOUNT</v-btn>
    </v-from>
  </div>
  <div class="account-info mb-8" v-else>
    <SfAlert
        class="mb-4"
        icon="mdi-account"
        message="Your account was created automatically. We have sent you a temporary password, which you can change at any time, as well as all the information provided during the checkout." />
    <h3 class="checkout__sub-title my-4">Account information</h3>
    <div class="panel mt-4">
      <v-row>
        <v-col class="fully-justify-v">
          <div class="item">
            <label>Name:</label>
            <p>Volodymyr Lazarev</p>
          </div>
        </v-col>
        <v-col>
          <div class="item">
            <label>Contacts:</label>
            <p></p>
          </div>
        </v-col>
      </v-row>
    </div>
  </div>
  <div class="order-places">
    <h3 class="checkout__sub-title my-6">Your Order was places successfully  </h3>
    <p class="mb-2">Check your email for your order confirmation</p>
    <p>Your Order: CO77588939004</p>
    <p>Order Date: 03/28/2024</p>
    <p>Your order has been successfully placed. An email confirming your order along with its details and a tracking link will be sent to you shortly.</p>
  </div>
  <div class="panel mt-4">
    <v-row>
      <v-col class="fully-justify-v">
        <v-spacer />
        <div class="item">
          <label>To whom</label>
          <p>Volodymyr Lazarev</p>
        </div>
        <v-spacer />
      </v-col>
      <v-col>
        <div class="item">
          <h3>Shipping Address</h3>
          <p>4517 Washington Ave. Manchester, Kentucky 39495</p>
          <label>Delivery instruction</label>
          <p>+Please leave the package by the door.</p>
        </div>
      </v-col>
    </v-row>
  </div>
  <div class="shipping-method">
    <h3 class="checkout__sub-title my-6">Payment Method</h3>
    <div class="panel fully-justify mb-4">
      <span>PayPal</span>
      <v-icon icon="mdi-account" />
    </div>
    <v-row class="ma-0">
      <v-col cols="6" class="pa-1">Nord Street St 10016 89183</v-col>
      <v-col cols="6" class="pa-1">Appartment 7</v-col>
      <v-col cols="6" class="pa-1">United State of America</v-col>
      <v-col cols="6" class="pa-1">Nevada</v-col>
      <v-col cols="6" class="pa-1">Las Vegas </v-col>
      <v-col cols="6" class="pa-1">0987665</v-col>
    </v-row>
  </div>
  <v-btn
      class="mt-12"
      variant="elevated"
      height="52"
      rounded="0"
      color="black"
      block
      type="submit"
  >GO TO SHOPPING</v-btn>
</template>

<script setup>
import { ref } from 'vue'
import SfAlert from '@/components/SfAlert';

const createAccFlag = ref(true)

</script>

<style lang="scss" scoped>

</style>
