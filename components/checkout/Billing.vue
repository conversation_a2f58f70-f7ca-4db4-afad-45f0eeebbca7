<template>
  <div class="billing">
    <div class="title">
      <h3>Billing address  </h3>
      <v-checkbox v-model="defaultBilling" label="Use shipping address as billing address" hide-details/>
    </div>
    <div class="">
      <v-btn>Select Address</v-btn>
    </div>
    <v-form v-show="!defaultBilling" validate-on="submit lazy" @submit.prevent="handleShipping">
      <v-text-field
          label="Full name"
          prefix="@"
      ></v-text-field>
      <v-text-field
          label="Street address"
          prefix="@"
      ></v-text-field>
      <v-text-field
          label="Apartment or suite number"
          prefix="@"
      ></v-text-field>
      <v-row>
        <v-col cols="6">
          <v-text-field
              label="Country"
              prefix="@"
          ></v-text-field>
        </v-col>
        <v-col cols="6">
          <v-text-field
              label="State/Province"
              prefix="@"
          ></v-text-field>
        </v-col>
        <v-col cols="6">
          <v-text-field
              label="City"
              prefix="@"
          ></v-text-field>
        </v-col>
        <v-col cols="6">
          <v-text-field
              label="Zip-code"
              prefix="@"
          ></v-text-field>
        </v-col>
      </v-row>
    </v-form>
  </div>
</template>

<script setup>
import { ref } from 'vue'
const defaultBilling = ref(false)
const handleShipping = () => {}
</script>

<style lang="scss" scoped>
.billing {
  .title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 28px;
    line-height: 28px;
    margin: var(--spacer-sm) 0;
  }
}
</style>
