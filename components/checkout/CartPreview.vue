<template>
  <div class="summary">
    <h3>{{$t('Order summary')}}</h3>
    <div class="products">
      <SfProduct type="view" v-for="n in [1,2,3]"/>
    </div>
    <div class="promo mt-8">
      <v-text-field
          height="52"
          placeholder="ENTER PROMO CODE"
          hide-details
      />
      <v-btn class="ml-4" color="black" height="52">{{$t('Apply')}}</v-btn>
    </div>
    <div class="summary-order mt-4 pa-8">
      <div class="desktop-only">
        <div class="property">
          <div class="property__name">Item price:</div>
          <div class="property__value">$ 130.00</div>
        </div>
        <div class="property">
          <div class="property__name">Item(s) Subtotal:</div>
          <div class="property__value">$ 130.00</div>
        </div>
        <div class="property">
          <div class="property__name"></div>
          <div class="property__value">---------</div>
        </div>
        <div class="property">
          <div class="property__name">Total before tax:</div>
          <div class="property__value">$ 130.00</div>
        </div>
        <div class="property">
          <div class="property__name">Tax (incl.):</div>
          <div class="property__value">$ 130.00</div>
        </div>
        <div class="property">
          <div class="property__name"></div>
          <div class="property__value">---------</div>
        </div>
      </div>
      <div class="property">
        <div class="property__name"><b>Total:</b></div>
        <div class="property__value"><b> $ 330.00</b></div>
      </div>
    </div>
    <div class="easy-shipping mt-4 px-8 py-4">
      <h3 class="d-flex items-center"><v-icon icon="mdi-rss-box" />Easy shipping</h3>
      <p class="mt-2">You’ll receive dispatch confirmation and an arrival sade</p>
    </div>
  </div>
</template>

<script setup>
</script>

<style lang="scss" scoped>
@use '~/assets/styles/default' as *;
.promo {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .v-field__field {
    height: 52px;
  }
  .v-field__overlay {
    border-radius: 0px;
  }
  .v-field__outline {
    display: none;
  }
}
.summary-order {
  background: var(--grey-100);
  .property {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 24px;
    line-height: 24px;
    margin-bottom: 8px;
    text-align: right;
    &__name, &__value {
      width: 40%;
    }
    @include for-mobile {
      &__name {
        text-align: left;
      }
    }
  }
}
.easy-shipping {
  background: var(--grey-100);
}
</style>
