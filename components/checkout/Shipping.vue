<template>
  <div class="shipping">
    <h3 class="checkout__title">1.Shipping</h3>
    <div class="shipping-content" v-if="editFlag">
      <h3 class="checkout__sub-title">Shipping address </h3>
      <SfAlert
          icon="mdi-magnify"
          message="Please add an address where you would like your order to be shipped. This allow us to determine the most favorable method of delivery of goods from our store for you."
      />
      <v-form validate-on="submit lazy" @submit.prevent="handleShipping">
        <p class="py-4">Customer information</p>
        <v-text-field
            v-model="formData.email"
            label="Email"
            prepend-inner-icon="mdi-at"
            clearable
            @blur.prevent="checkEamil"
        ></v-text-field>
        <v-text-field
            v-model="formData.phone"
            label="Phone number"
            prepend-inner-icon="mdi-cellphone"
            clearable
        ></v-text-field>
        <v-text-field
            v-model="formData.full_name"
            label="Full Name"
            prepend-inner-icon="mdi-account"
            clearable
        ></v-text-field>
        <p class="py-2">Shipping address</p>
        <v-text-field
            v-model="formData.address"
            label="Street address"
            prepend-inner-icon="mdi-notebook-outline"
            clearable
        ></v-text-field>
        <v-text-field
            v-model="formData.suite_number"
            label="Apartment or suite number"
            prepend-inner-icon="mdi-notebook-outline"
            clearable
        ></v-text-field>
        <v-row>
          <v-col cols="6">
            <v-select
                v-model="formData.country"
                :items="['Algeria']"
                label="Country"
                prepend-inner-icon="mdi-notebook-outline"
                clearable
            ></v-select>
          </v-col>
          <v-col cols="6">
            <v-select
                v-model="formData.state"
                :items="['Algeria']"
                label="State/Province"
                prepend-inner-icon="mdi-notebook-outline"
                clearable
            ></v-select>
          </v-col>
          <v-col cols="6">
            <v-text-field
                v-model="formData.city"
                label="City"
                prepend-inner-icon="mdi-notebook-outline"
                clearable
            ></v-text-field>
          </v-col>
          <v-col cols="6">
            <v-text-field
                v-model="formData.zip_code"
                label="Zip-code"
                prepend-inner-icon="mdi-notebook-outline"
                clearable
            ></v-text-field>
          </v-col>
        </v-row>
        <v-checkbox
            v-model="formData.use_address"
        >
          <template v-slot:label>
            <b>Use this address for billing</b>
          </template>
        </v-checkbox>
        <v-row>
          <v-col>
            <v-checkbox
                v-model="formData.faster_checkout"
            >
              <template v-slot:label>
                <b>Save information for a faster checkout</b>
              </template>
            </v-checkbox>
          </v-col>
          <v-col>
            <p>We will create the account for you and send a password on your email </p>
          </v-col>
        </v-row>
        <v-btn
            variant="elevated"
            height="52"
            rounded="0"
            color="black"
            block
            type="submit"
            :disabled="canSubmit"
        >CONTINUE TO SHIPPING METHOD</v-btn>
      </v-form>
    </div>
    <div class="shipping-content" v-else>
      <h3 class="checkout__sub-title">Customer information </h3>
      <div class="panel">
        <v-row>
          <v-col class="fully-justify-v">
            <div class="item">
              <label>Name</label>
              <p>Volodymyr Lazarev</p>
            </div>
          </v-col>
          <v-col>
            <div class="item">
              <label>Contacts:</label>
              <p><EMAIL></p>
              <p>+**************</p>
            </div>
          </v-col>
        </v-row>
        <div class="actions">
          <v-btn variant="text" class="text-decoration-underline text-capitalize">Change</v-btn>
        </div>
      </div>
      <h3 class="checkout__sub-title">Shipping address </h3>
      <SfAddressPicker />
    </div>
  </div>
  <LoginModel v-model="checkEamilFlag" title="Checkout">
    <div class="mx-6 my-4">
      <v-sheet color="#F1F5FF" class="d-flex align-center justify-content-between pa-3">
        <v-icon class="ma-2" icon="mdi-shield-account" size="42" color="#1C77FF"/>
        <h5 class="ml-2">We found your record as a customer in our store. Please enter your password to log in to your account or continue shopping as a guest </h5>
      </v-sheet>
    </div>
  </LoginModel>
  <!--choose address-->
  <v-dialog class="checkout-dialog" width="680" v-model="chooseAddrFlag">
    <v-container class="bg-white" v-if="!editAddrFlag">
      <div class="fully-justify">
        <h4>Choose your address </h4>
        <v-btn icon="mdi-close" variant="text" @click="chooseAddrFlag = false"/>
      </div>
      <v-radio-group class="py-4" hide-details>
        <v-radio class="mb-4" v-for="item in [1,2,3]" :value="item">
          <template v-slot:label>
            <b>Volodymyr Lazarev</b>
            <span>4517 Washington Ave. Manchester, Kentucky 39495</span>
            <v-btn variant="text" class="text-decoration-underline text-capitalize" @click="editAddrFlag = true">{{$t('Edit')}}</v-btn>
          </template>
        </v-radio>
      </v-radio-group>
      <div class="delivery pa-2">
        <span class="text-blue-darken-4"
              v-if="!editDeliverFlag"
              @click="editDeliverFlag = true"
        >+ Add delivery instruction</span>
        <v-text-field
          v-else
          label="Delivery Instruction"
          active
          hide-details
          clearable
        />
      </div>
      <div class="fully-justify">
        <span>Shipping address</span>
        <v-btn variant="text" class="text-decoration-underline text-capitalize">Add new address</v-btn>
      </div>
      <v-checkbox label="Use this address for shipping all product in order"></v-checkbox>
      <v-row>
        <v-col>
          <v-btn variant="tonal" height="52" rounded="0" block>CANCEL</v-btn>
        </v-col>
        <v-col>
          <v-btn variant="flat" color="black" height="52" rounded="0" block>APPLY</v-btn>
        </v-col>
      </v-row>
    </v-container>
    <v-container v-else class="bg-white">
      <div class="fully-justify">
        <h4><v-icon icon="mdi-arrow-left" class="mr-2"/>Choose your address </h4>
        <v-btn icon="mdi-close" variant="text" @click="chooseAddrFlag = false"/>
      </div>
      <v-form validate-on="submit lazy" @submit.prevent="handleShipping">
        <p class="py-2">Shipping address</p>
        <v-text-field
            v-model="formData.full_name"
            label="Full Name"
            prepend-inner-icon="mdi-account"
            clearable
        ></v-text-field>
        <v-text-field
            v-model="formData.address"
            label="Street address"
            prepend-inner-icon="mdi-notebook-outline"
            clearable
        ></v-text-field>
        <v-text-field
            v-model="formData.suite_number"
            label="Apartment or suite number"
            prepend-inner-icon="mdi-notebook-outline"
            clearable
        ></v-text-field>
        <v-row>
          <v-col cols="6">
            <v-select
                v-model="formData.country"
                :items="['Algeria']"
                label="Country"
                prepend-inner-icon="mdi-notebook-outline"
                clearable
            ></v-select>
          </v-col>
          <v-col cols="6">
            <v-select
                v-model="formData.state"
                :items="['Algeria']"
                label="State/Province"
                prepend-inner-icon="mdi-notebook-outline"
                clearable
            ></v-select>
          </v-col>
          <v-col cols="6">
            <v-text-field
                v-model="formData.city"
                label="City"
                prepend-inner-icon="mdi-notebook-outline"
                clearable
            ></v-text-field>
          </v-col>
          <v-col cols="6">
            <v-text-field
                v-model="formData.zip_code"
                label="Zip-code"
                prepend-inner-icon="mdi-notebook-outline"
                clearable
            ></v-text-field>
          </v-col>
        </v-row>
        <v-row>
          <v-col>
            <v-btn variant="tonal" height="52" rounded="0" block @click="editAddrFlag = false">CANCEL</v-btn>
          </v-col>
          <v-col>
            <v-btn variant="flat" color="black" height="52" rounded="0" block>SAVE CHANGES</v-btn>
          </v-col>
        </v-row>
      </v-form>
    </v-container>
  </v-dialog>
</template>

<script setup>
import {ref,reactive, computed} from 'vue'
import SfAlert from '@/components/SfAlert';
import LoginModel from '@/components/LoginModal';
import SfAddressPicker from '@/components/SfAddressPicker.vue';

const props = defineProps(['step'])
const emit = defineEmits(['update'])
const chooseAddrFlag = ref(false)
const editAddrFlag = ref(true)
const editFlag = ref(false)
const editDeliverFlag = ref(false)
const checkEamilFlag = ref(false)
const formData = reactive({
  email: '',
  phone: '',
  full_name: '',
  use_address: '',
  suite_number: '',
  country: '',
  state: '',
  city: '',
  zip_code: '',
  faster_checkout: '',
})
const canSubmit = computed(() => {
  return !(formData.email && formData.phone && formData.full_name && formData.country && formData.city && formData.zip_code)
})
const checkEamil = () => {
  checkEamilFlag.value = true
}
const handleShipping = async (evt) => {
  console.log('handle shipping')
  const { valid } = await evt.then()

  if (!valid) {
    return false
  }

  editFlag.value = false
  emit('update', true)
}
</script>

<style lang="scss" scoped>
.shipping {
  &-desc {
    display: flex;
    align-items: center;
    background: #F1F5FF;
    padding: 16px;
  }
  .login-desc {
    display: flex;
    align-items: center;
    background: #F1F5FF;
    padding: 16px;
  }
}
</style>
