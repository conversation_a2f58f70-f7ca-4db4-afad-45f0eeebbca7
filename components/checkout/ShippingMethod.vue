<template>
  <div class="shipping-method">
    <h3 class="checkout__title">2.Shipping Method</h3>
    <div class="shipping-method-desc" v-if="props.step == 1">
      Enter your shipping address to view available shipping  method
    </div>
    <template v-else>
      <h3 class="checkout__sub-title">Items in order (3) </h3>
      <v-form v-if="editFlag && props.step == 2" class="shipping-method-list" validate-on="submit lazy" @submit.prevent="handleSubmit">
        <div class="shipping-method-item" v-for="item in [1,2,3]">
          <SfProduct @edit="handleEdit(item)"/>
          <div class="font-weight-bold text-black pt-4">Choose sipping delivery option</div>
          <v-radio-group class="py-4" hide-details>
            <v-radio label="Free delivery during 2-4 days" value="one" @click="informFlag = true"></v-radio>
            <v-radio label="USPS First / USPS First - $ 5.00" value="two"></v-radio>
            <v-radio label="USPS First / USPS First - $ 7.00" value="three"></v-radio>
          </v-radio-group>
        </div>
        <v-btn
            variant="elevated"
            height="52"
            rounded="0"
            color="black"
            block
            type="submit"
        >CONTINUE TO SHIPPING METHOD</v-btn>
      </v-form>
      <div v-else class="panel py-1">
        <div class="d-flex align-center my-2" v-for="item in [1,2,3]">
          <img src="https://testmart.axel.market/images/default.jpg" width="42" height="42"/>
          <div class="ml-4">
            <h5>Name</h5>
            <p>Free Local Pickup</p>
          </div>
        </div>
        <div class="actions">
          <v-btn variant="text" class="text-decoration-underline text-capitalize">Change</v-btn>
        </div>
      </div>
    </template>
  </div>
  <!--pay attention-->
  <v-dialog width="800" height="560" v-model="informFlag">
    <div class="pay-attention bg-white pa-8 mt-2">
      <div class="fully-justify-v position-relative">
        <v-btn class="close" icon="mdi-close" variant="text" @click="informFlag = false"/>
        <p class="text-h4 mb-8">PAY YOUR ATTENCTION!</p>
        <p class="mb-8">There are items in your cart that are only available for local pickup and not available for shipping. Please confirm that you can pick up from the address below before you proceed with payment. </p>
        <p class="w-50 margin-auto font-weight-bold mb-8">5538 S.Eastern Avenue Las Vegas, Nevada 89119 United States   </p>
        <div class="justify-right">
          <v-btn rounded="0" size="large" width="172" color="black">{{$t('Sign up')}}</v-btn>
        </div>
      </div>
    </div>
  </v-dialog>
  <!--edit product-->
  <v-dialog width="800" height="560" v-model="editDialog">
    <v-container class="bg-white">
      <div class="d-flex">
        <div class="bg-grey-lighten-4">
          <v-img src="https://testmart.axel.market/images/default.jpg" width="450" height="520" />
        </div>
        <div class="fully-justify-v pl-6">
          <div class="justify-right">
            <v-btn icon="mdi-close" variant="text" @click="editDialog = false"/>
          </div>
          <div class="title mb-8">
            <h1>The Mother's Day Box 2024</h1>
            <p>Product item code: P75748989B</p>
          </div>
          <div class="price">
            <h3>$ 45.99</h3>
            <p>Tax included:</p>
          </div>
          <v-spacer />
          <div class="actions fully-justify-v">
            <label class="mb-4">Quantity</label>
            <v-number-input
                class="mb-4"
                control-variant="split"
                variant="outlined"
                rounded="0"
                height="52"
                hide-details
            ></v-number-input>
            <v-btn class="mb-6" height="52" rounded="0" color="black">{{$t('Save')}}</v-btn>
            <v-btn variant="text">
              <b class="text-decoration-underline">{{$t('Remove')}}</b>
            </v-btn>
          </div>
        </div>
      </div>
    </v-container>

  </v-dialog>
</template>

<script setup>
import { ref } from 'vue'
import SfProduct from '@/components/SfProduct'

const props = defineProps(['step'])
const emit = defineEmits(['update'])
const editFlag = ref(true)
const informFlag = ref(false)
const editDialog = ref(false)
const handleSubmit = async (evt) => {
  console.log('handle shipping method')
  const { valid } = await evt.then()

  if (!valid) {
    return false
  }

  editFlag.value = false
  emit('update', true)
}
const handleEdit = () => {
  editDialog.value = true
}
</script>

<style lang="scss" scoped>
.shipping-method {
  &-desc {
    background: var(--grey-100);
    padding: 8px 16px;
    line-height: 32px;
  }
  &-item {
    border-bottom: 1px solid var(--grey-500);
  }
  &-item:last-child {
    border-bottom: 0px;
  }

  :deep(.v-number-input) {
    .v-field__field {
      height: 44px;
    }
  }
}
.pay-attention {
  .close {
    position: absolute;
    top: -5px;
    right: 0px;
  }
}
</style>
