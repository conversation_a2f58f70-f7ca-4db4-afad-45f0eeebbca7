<template>
  <div class="payment">
    <h3 class="checkout__title">3.Payment</h3>
    <div class="payment-content" v-if="props.step == 3">
      <Billing />
      <h3 class="checkout__sub-title">Payment Method </h3>
      <v-form class="payment-list">
        <v-radio-group class="py-4" hide-details>
          <v-radio value="one">
            <template v-slot:label>
              <label>Stripe Element</label>
              <v-icon icon="mdi-home" class="mr-4"/>
            </template>
          </v-radio>
          <v-radio label="PayPal" value="two"></v-radio>
          <v-radio label="Ebay Payment" value="three"></v-radio>
        </v-radio-group>
        <v-checkbox>
          <template v-slot:label>
            I agree to <a class="text-decoration-underline ml-2">Terms and Conditions</a>
          </template>
        </v-checkbox>
        <v-btn variant="elevated" height="52" rounded="0" color="black" block>PLACE THE ORDER</v-btn>
      </v-form>
    </div>
  </div>
</template>

<script setup>
import Billing from './Billing'

const props = defineProps(['step'])
const handleShipping = () => {}
</script>

<style lang="scss" scoped>

</style>
