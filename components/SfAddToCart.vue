<template>
  <div class="sf-add-to-cart">
    <slot name="quantity-select-input" v-bind="{ qty }">
      <div class="sf-add-to-cart__select-quantity">
        <v-number-input
            control-variant="split"
            variant="outlined"
            width="150"
            height="52"
            rounded="0"
            hide-details
            :disabled="disabled"
        ></v-number-input>
      </div>
    </slot>
    <slot name="add-to-cart-btn">
      <!--@slot Custom content that will replace default Add to cart button design.-->
      <v-btn
          class="sf-add-to-cart__button"
          :disabled="disabled"
          variant="tonal"
          rounded="0"
          height="52"
          v-on="$listeners"
      >
        {{$t('Add to cart')}}
      </v-btn>
    </slot>
  </div>
</template>

<script setup>
import { VNumberInput } from 'vuetify/labs/VNumberInput'
const props = defineProps({
  /**
   * Boolean to indicate whether product
   * can be added to cart
   */
  disabled: {
    type: Boolean,
    default: false,
  },
  /**
   * Selected quantity
   */
  qty: {
    type: [Number, String],
    default: 1,
  },
})
</script>

<style lang="scss" scoped>
.sf-add-to-cart {
  display: flex;
  margin: var(--spacer-sm) 0 var(--spacer-sm);
  &__select-quantity {
    width: 180px;
    .v-field {
      height: 52px;
    }
  }
  &__button {
    flex: 1;
  }
}
</style>
