<template>
  <header class="header" :class="'header-' + props.type">
    <div class="header-container">
      <div class="header-left">
        <nuxt-link-locale v-if="props.data.logo_visible" to="/">
          <img v-if="props.data.logo_url" :src="targetUrl + props.data.logo_url" width="80" height="80"/>
          <img v-else src="~/assets/images/logo.png" width="80" height="80"/>
        </nuxt-link-locale>
        <span v-else>MY STORE</span>
      </div>
      <div class="header-center" v-if="props.type != 'simple'">
        <client-only>
          <SearchBar />
        </client-only>
      </div>
      <div class="header-right" v-if="props.type != 'simple'">
        <div class="smartphone-only">
          <v-bottom-sheet>
            <template v-slot:activator="{ props: activatorProps }">
              <v-btn v-bind="activatorProps"  class="mr-2" icon="mdi-format-list-bulleted-square" variant="text"></v-btn>
            </template>

            <v-card>
              <v-list>
                <v-list-item>Big iPhone Offer</v-list-item>
                <v-list-item>Shop all</v-list-item>
                <v-list-item>Books</v-list-item>
              </v-list>
              <template v-slot:actions>
                <v-btn
                    color="black-lighten-2"
                    text="Cancel"
                    variant="outlined"
                    block
                ></v-btn>
              </template>
            </v-card>
          </v-bottom-sheet>
        </div>
        <div class="desktop-only">
          <v-btn class="mr-2" icon="mdi-account-outline" variant="text" @click="handleLogin"></v-btn>
          <v-btn class="mr-2" icon="" variant="text" @click="handleOpen('wishlist')">
            <v-badge :content="2" color="error"><v-icon icon="mdi-heart-outline" variant="text"></v-icon></v-badge>
          </v-btn>
          <v-btn class="mr-2" icon="" variant="text" @click="handleOpen('cart')">
            <v-badge :content="4" color="error"><v-icon icon="mdi-cart-outline" variant="text"></v-icon></v-badge>
          </v-btn>
          <LocaleSelector></LocaleSelector>
        </div>
      </div>
    </div>
  </header>
</template>
<script setup>
import { ref, inject, reactive, readonly } from 'vue'
import { useRouter } from 'vue-router'

// const { targetUrl } = useRuntimeConfig().public;
const targetUrl = 'https://newmarket.viacube.com/'

const router = useRouter()
const props = defineProps({
  type: {
    type: String,
    default: ''
  },
  data: {
    type: Object,
    default() {
      return {}
    }
  }
})
const emit = defineEmits('open')

const loginFlag = ref(false)
const wishlistFlag = ref(false)
const cartFlag = ref(false)

const handleLogin = () => {
  router.push('/signin')
}
const handleOpen = (type) => {
  emit('open', type)
}

</script>

<style lang="scss" scoped>
@use '~/assets/styles/default' as *;
.header {
  position: relative;
  background: white;
  z-index: 2;
  &-simple {
    border-bottom: 1px solid #000;
  }
  &-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1000px;
    height: 82px;
    margin: 0 auto;
  }
  &-left {
    .sf-header--logo {
      width: 82px;
      height: 82px;
    }
  }
  &-center {
    position: relative;
    flex: 0 0 40%;
    display: flex;
    align-items: center;
  }

  @include for-mobile {
    height: auto;
    padding: 16px;
    &-container {
      flex-direction: row-reverse;
    }
    &-center {
      flex: 1;
      margin: 0 50px;
    }
  }
}

</style>
