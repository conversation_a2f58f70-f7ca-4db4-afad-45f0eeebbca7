<template>
  <div class="navbar__main">
    <div class="navbar__view">
      <h3>WELLNESS</h3>
      <span class="pl-4 text-grey">
        {{ props.total }} products
      </span>
    </div>
    <div class="navbar__options desktop-only">
      <span class="font-weight-bold cursor-pointer" @click="toggleFilterSidebar">
        {{$t('Filters')}}:
      </span>
      <div class="navbar__option pl-4">
        <v-chip class="mr-2" closable>
          $25.00-$50.00
        </v-chip>
        <v-chip class="mr-2" closable>
          Men
        </v-chip>
        <v-chip class="mr-2" closable>
          Medium
        </v-chip>
        <v-chip class="mr-2" closable>
        Type: Vitamin C
        </v-chip>
        <v-chip class="mr-2" closable>
          Type: Vitamin C
        </v-chip>
        <v-chip class="mr-2" closable>
          Type: Vitamin C
        </v-chip>
        <v-chip class="mr-2" closable>
          Type: Vitamin C
        </v-chip>
        <b class="text-decoration-underline cursor-pointer" @click="handleClearAll">clear all</b>
      </div>
      <div class="navbar__sort desktop-only">
        <div class="navbar__label">
          <b>{{ $t('Sort by') }}:</b>
        </div>
        <v-select
            v-model="filters.sort"
            class="navbar__select"
            width="200"
            density="compact"
            bg-color="white"
            :placeholder="$t('Select sorting')"
            :items="sorts"
            item-title="name"
            item-value="value"
            @update:modelValue="changeFilter"
            hide-details
        ></v-select>
      </div>
    </div>
  </div>

  <FiltersSidebar v-model="sidebarFlag" @close="toggleFilterSidebar"/>
</template>
<script setup>
import { ref, inject, computed, reactive, readonly } from 'vue'
import { useRouter } from 'vue-router'
import FiltersSidebar from './FiltersSidebar'

const productStore = useProduct();
const { filters, changeFilters } = storeToRefs(productStore);
const router = useRouter()

const props = defineProps(['modelValue', 'total'])
const emit = defineEmits(['update:modelValue', 'view', 'clear'])
const sidebarFlag = ref(false)
const sorts = readonly([{
  value: 'price',
  name: 'Price, low to high'
}, {
  value: '-price',
  name: 'Price, low to low'
}])

const changeFilter = (value) => {
  productStore.changeFilters({
    sort: value
  })
}
const toggleFilterSidebar = () => {
  sidebarFlag.value = !sidebarFlag.value
}
const handleClearAll = () => {
  emit('clear')
}
</script>

<style lang="scss" scoped>
@use '~/assets/styles/default' as *;
.navbar__main {
  .navbar__sort {
    display: flex;
    align-items: center;
    padding-left: 48px;
    height: 32px;
  }
  .navbar__label {
    text-wrap-mode: nowrap;
  }
  .navbar__counter {
    padding-right: 48px;
  }
  .navbar__view {
    display: flex;
    align-items: center;
    height: 48px;
    line-height: 48px;
    @include for-mobile {
      justify-content: space-between;
    }
  }
  .navbar__options {
    display: flex;
    line-height: 32px;
    margin-top: 8px;
  }
  .navbar__option {
    flex: 1;
  }
}
.v-chip--variant-tonal .v-chip__underlay {
  background: var(--grey-100);
}
.v-chip {
  margin-right: 4px;
  margin-bottom: 8px;
}
.navbar__select.v-field__input {
  padding: 0 6px;
}
</style>
