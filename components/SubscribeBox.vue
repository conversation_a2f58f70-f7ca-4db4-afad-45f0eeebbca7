<template>
  <div class="subscribe-box py-10 text-center">
    <div class="title">Subscribe to our emails</div>
    <div class="sub-title my-4">{{$t('Subscribe to Newsletters title')}}</div>
    <v-text-field
        class="subscribe-input"
        append-inner-icon="mdi-arrow-right"
        density="compact"
        clearable
        hide-details
        variant="outlined"
        height="42"
        rounded="0"
        :placeholder="$t('Email')"
    ></v-text-field>
  </div>
</template>

<script setup>

</script>

<style lang="scss">
@use '~/assets/styles/default' as *;
.subscribe-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 auto;
  width: 526px;
  @include for-mobile {
    width: 100%;
    max-width: 526px;
    padding: 0 24px;
    .sub-title {
      text-align: left;
    }
  }
  .title {
    font-size: 32px;
  }
  .subscribe-input {
    width: 388px;
    @include for-mobile {
      width: 100%;
      max-width: 388px;
    }
  }
}
</style>
