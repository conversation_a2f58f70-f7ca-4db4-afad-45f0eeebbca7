<template>
  <v-card variant="text">
    <slot name="header"></slot>
    <v-card-item>
      <h2>{{props.data?.title}}</h2>
      <p><b>Product item code: {{props.data?.product?.slug}}</b></p>
    </v-card-item>
    <slot name="more"></slot>
    <v-card-item>
      <h2>{{props.data?.default_variant?.listing_inventory?.display_price}}</h2>
      <b>Tax included:</b>
    </v-card-item>
    <v-card-item>
      <h3>Quantity</h3>
      <v-number-input
          control-variant="split"
          variant="outlined"
          height="52"
          rounded="0"
          hide-details
      ></v-number-input>
    </v-card-item>
    <v-card-item>
      <v-radio-group label="Payment option">
        <v-radio label="Buy one time" value="one"></v-radio>
        <v-radio label="Subscribe to save" value="two"></v-radio>
      </v-radio-group>
    </v-card-item>
    <v-card-item>
      <v-btn variant="elevated" color="black" rounded="0" height="53" block>ADD TO CARD</v-btn>
    </v-card-item>
    <v-card-item>
      <div v-if="product && (product.temporaryUnavailable || !product.inStock)" class="product__notify-me">
        <p class="mb-4">{{ $t('Notify Me text') }}</p>
        <v-btn
            variant="elevated"
            color="black"
            rounded="0"
            height="53"
            :disabled="notifyLoading"
            @click="handleNotifyMe(product.temporaryUnavailable ? 'temporaryUnavailable' : 'outOfStock')"
        >
          {{ $t('Notify Me') }}
        </v-btn>
      </div>
    </v-card-item>
    <slot name="tab"></slot>
  </v-card>
</template>

<script setup>
const props = defineProps(['data'])

const product = ref(props.data.product)
const notifyLoading = ref(false)

const handleNotifyMe = () => {

}
</script>

<style lang="scss" scoped>
.product-cart {
  position: absolute;
  bottom: 66px;
  height: 48px;
  display: none;
}
.product-view {
  position: absolute;
  bottom: 10px;
  height: 48px;
  background: radial-gradient(circle, white, transparent);
  display: none;
}
.product-wish {
  position: absolute;
  top: 10px;
  right: 10px;
  display: none;
}
.v-card:hover {
   .product-image {
     background: var(--grey-100);
     transform: scale(2.5);
     transition: transform 0.5s ease;
   }
  .product-cart,.product-view,.product-wish {
    display: block;
  }
}
</style>
