<template>
  <div class="product-price">
    <div class="product-price__card">
      <ins class="price__special">
        {{ props.data?.listing_inventory?.display_price }}
      </ins>
      <del class="price__old">
        {{ props.data?.listing_inventory?.compare_to_price }}
      </del>
    </div>
  </div>
</template>
<script setup>
import { ref, inject, computed, reactive, readonly } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const props = defineProps(['data'])
</script>

<style lang="scss" scoped>
.product-price {
  .price__special {
    font-family: Raleway;
    color: #b22509;
    font-size: 16px;
    line-height: 24px;
    font-weight: 700;
    margin-right: 5px;
  }
  .price__old {
    color: #666;
    font-size: 14px;
    margin-right: 5px;
  }
  .percent {
    display: inline-block;
    width: 32px;
    height: 24px;
    text-align: center;
    line-height: 24px;
    border-radius: 2px;
    background: #b22509;
    color: #fff;
    font-size: 12px;
  }
  .product-price__compare {
    font-size: 12px;
  }
}
</style>
