<template>
  <v-overlay v-model="props.modelValue" class="d-flex justify-end">
    <div class="side-modal fully-justify-v" rounded="0">
      <div class="fully-justify">
        <h4 v-if="products.length > 0">In your cart (3 items)</h4>
        <v-spacer />
        <v-btn icon="mdi-close" variant="text" @click="toggleCartSidebar"></v-btn>
      </div>
      <div class="sidebar-main" v-if="products.length > 0">
        <v-data-table
            :headers="headers"
            :items="products"
            hide-default-footer
        >
          <template v-slot:item="{ item }">
            <tr>
              <td rowspan="3" class="pt-4" valign="top">
                <img :src="item.image" width="80" height="80"/>
                <div class="text-center">Remove</div>
              </td>
              <td colspan="3" class="no-border pt-4">
                <h3>{{ item.name }}</h3>
                <p>Product item code: P75748989B</p>
              </td>
            </tr>
            <tr>
              <td class="no-border text-right">{{ item.price }}</td>
              <td class="no-border">
                <v-number-input
                    width="180"
                    height="40"
                    variant="outlined"
                    control-variant="split"
                    hide-details
                ></v-number-input>
              </td>
              <td align="right" class="no-border">
                <p class="text-decoration-line-through">$49.99 </p>
                <p class="text-green">Your disc. 10% </p>
                <h3>{{ item.price }}</h3>
              </td>
            </tr>
            <tr>
              <td colspan="3" class="pb-4">
                <p class="text-center">$ 20.99 every week, all shipped upfront</p>
              </td>
            </tr>
          </template>
        </v-data-table>
      </div>
      <div class="sidebar-main text-center" v-else>
        <img src="~/assets/images/cart.svg" width="180" />
        <h2 class="mt-4">{{$t('Your cart is empty')}}</h2>
        <p class="mt-4">Today is the best day for shopping</p>
      </div>
      <div class="sidebar-footer" v-if="products.length > 0">
        <v-sheet class="pa-4 my-4" color="#f7f7f7">
          <div class="fully-justify">
            <h3>Total:</h3>
            <h3>$ 330.00</h3>
          </div>
          <div class="text-right">
            Taxes & shipping calculated at  checkout
          </div>
        </v-sheet>
        <div class="fully-justify">
          <v-btn
              variant="flat"
              size="large"
              :text="$t('Go back shopping')"
              height="50"
              rounded="0"
              @click="toggleCartSidebar"
          ></v-btn>
          <v-btn
              variant="flat"
              size="large"
              :text="$t('GO TO CHECKOUT')"
              color="black"
              height="50"
              rounded="0"
              @click="toggleCartSidebar"
          ></v-btn>
        </div>
      </div>
      <div class="sidebar-footer" v-else>
        <h3 class="my-4">{{$t('Featured Products')}}</h3>
        <v-row>
          <v-col  v-for="product in products">
            <img :src="product.image" width="120" />
            <p>{{product.name}}</p>
            <b>{{product.price}}</b>
          </v-col>
        </v-row>
        <v-btn
            variant="flat"
            size="large"
            :text="$t('Go back shopping')"
            color="black"
            width="100%"
            height="50"
            rounded="0"
            @click="toggleCartSidebar"
        ></v-btn>
      </div>
    </div>
  </v-overlay>
</template>
<script setup>
import {ref, inject, computed, reactive, readonly, watch} from 'vue'
import { useRouter } from 'vue-router'

const cartStore = useCartlist()

const router = useRouter()
const props = defineProps(['modelValue'])
const emit = defineEmits(['update:modelValue'])
const headers = readonly([
  { title: 'PRODUCT', key: 'product', align: 'start', sortable: false, width: 80 },
  { title: 'PRICE', key: 'price', sortable: false, align: 'start' },
  { title: 'QUANTITY', key: 'quantity', sortable: false, align: 'start' },
  { title: 'TOTAL', key: 'total', sortable: false, align: 'end' }
])

const products = [{
  id: '1',
  name: 'Mounja Burn Drops For Weight LossMounja Burn Drops For Weight Loss',
  price: '$27',
  image: 'https://market.viacube.com/rails/active_storage/representations/proxy/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBBcjhkIiwiZXhwIjpudWxsLCJwdXIiOiJibG9iX2lkIn19--3d6a1262046c28015fa3b1f29a12028cb7825ff2/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdDRG9MWm05eWJXRjBTU0lJYW5CbkJqb0dSVlE2RTNKbGMybDZaVjloYm1SZmNHRmtXd2hwQWZCcEFmQjdCam9NWjNKaGRtbDBlVWtpQzJObGJuUmxjZ1k3QmxRNkNuTmhkbVZ5ZXdZNkRIRjFZV3hwZEhscFZRPT0iLCJleHAiOm51bGwsInB1ciI6InZhcmlhdGlvbiJ9fQ==--4b059456da4e83d2cba81b5a78c7a579ac51ee20/Untitled-1.jpg'
}, {
  id: '2',
  name: 'Mounja Burn Drops For Weight Loss',
  price: '$25',
  image: 'https://market.viacube.com/rails/active_storage/representations/proxy/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBBcjhkIiwiZXhwIjpudWxsLCJwdXIiOiJibG9iX2lkIn19--3d6a1262046c28015fa3b1f29a12028cb7825ff2/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdDRG9MWm05eWJXRjBTU0lJYW5CbkJqb0dSVlE2RTNKbGMybDZaVjloYm1SZmNHRmtXd2hwQWZCcEFmQjdCam9NWjNKaGRtbDBlVWtpQzJObGJuUmxjZ1k3QmxRNkNuTmhkbVZ5ZXdZNkRIRjFZV3hwZEhscFZRPT0iLCJleHAiOm51bGwsInB1ciI6InZhcmlhdGlvbiJ9fQ==--4b059456da4e83d2cba81b5a78c7a579ac51ee20/Untitled-1.jpg'
}, {
  id: '3',
  name: 'Mounja Burn Drops',
  price: '$27',
  image: 'https://market.viacube.com/rails/active_storage/representations/proxy/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBBcjhkIiwiZXhwIjpudWxsLCJwdXIiOiJibG9iX2lkIn19--3d6a1262046c28015fa3b1f29a12028cb7825ff2/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdDRG9MWm05eWJXRjBTU0lJYW5CbkJqb0dSVlE2RTNKbGMybDZaVjloYm1SZmNHRmtXd2hwQWZCcEFmQjdCam9NWjNKaGRtbDBlVWtpQzJObGJuUmxjZ1k3QmxRNkNuTmhkbVZ5ZXdZNkRIRjFZV3hwZEhscFZRPT0iLCJleHAiOm51bGwsInB1ciI6InZhcmlhdGlvbiJ9fQ==--4b059456da4e83d2cba81b5a78c7a579ac51ee20/Untitled-1.jpg'
}];

const handleStartShop = () => {
  toggleCartSidebar()
  router.push('/categories')
}
const toggleCartSidebar = () => {
  emit('update:modelValue', false)
}
watch(
    () => props.modelValue,
    (value) => {
      if (value) {
        cartStore.getList()
      }
    })
</script>

<style lang="scss" scoped>
.v-table .v-table__wrapper > table > tbody > tr > th {
  color: red !important;
}
.v-table .v-table__wrapper > table > tbody > tr:last-child > td {
  border-bottom: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
}
.v-table .v-table__wrapper > table > tbody > tr > td.no-border{
  border-bottom: none;
}
.v-number-input .v-field__field {
  height: 52px;
}
</style>
