<template>
  <v-overlay id="filters" v-model="props.modelValue" class="v-infinite-scroll--vertical">
    <v-card class="side-modal scrollbar" rounded="0">
      <v-card-title>
        Price
      </v-card-title>
      <v-card-text>
        <v-radio-group>
          <v-radio label="Under $25.00" value="one"></v-radio>
          <v-radio label="R$25.00 - $50.00" value="two"></v-radio>
          <v-radio label="Radio Three" value="three"></v-radio>
          <v-radio label="$100.00 - $200.00" value="4"></v-radio>
          <v-radio label="$200.00 & above" value="5"></v-radio>
        </v-radio-group>
      </v-card-text>
      <v-card-title>
        Gender
      </v-card-title>
      <v-card-item>
        <v-radio-group>
          <v-radio label="Men" value="one"></v-radio>
          <v-radio label="Women" value="two"></v-radio>
          <v-radio label="Unisex" value="three"></v-radio>
        </v-radio-group>
      </v-card-item>

      <v-card-title>
        Size
      </v-card-title>
      <v-card-item>
        <v-radio-group>
          <v-radio label="Small" value="one"></v-radio>
          <v-radio label="Medium" value="two"></v-radio>
          <v-radio label="Large" value="three"></v-radio>
        </v-radio-group>
      </v-card-item>

      <v-card-title>
        Color
      </v-card-title>
      <v-card-item>
        <v-row>
          <v-col>
            <v-radio-group>
              <v-radio
                  color="red"
                  label="red"
                  value="red"
              ></v-radio>
              <v-radio
                  color="red-darken-3"
                  label="red-darken-3"
                  value="red-darken-3"
              ></v-radio>
              <v-radio
                  color="indigo"
                  label="indigo"
                  value="indigo"
              ></v-radio>
            </v-radio-group>
          </v-col>
          <v-col>
            <v-radio-group>
              <v-radio
                  color="red"
                  label="red"
                  value="red"
              ></v-radio>
              <v-radio
                  color="red-darken-3"
                  label="red-darken-3"
                  value="red-darken-3"
              ></v-radio>
              <v-radio
                  color="indigo"
                  label="indigo"
                  value="indigo"
              ></v-radio>
            </v-radio-group>
          </v-col>
        </v-row>
      </v-card-item>

      <v-card-title>
        Type
      </v-card-title>
      <v-card-item>
        <v-radio-group>
          <v-radio label="Small" value="one"></v-radio>
          <v-radio label="Medium" value="two"></v-radio>
          <v-radio label="Large" value="three"></v-radio>
        </v-radio-group>
      </v-card-item>

      <v-card-actions class="filter-footer">
        <div class="d-flex flex-column filter-footer-content">
          <v-btn
              variant="flat"
              size="large"
              text="APPLY"
              color="black"
              width="100%"
              height="50"
              class="mb-2"
              @click="applyFilters"
          ></v-btn>
          <v-btn
              variant="tonal"
              size="large"
              :text="$t('Clear all')"
              width="100%"
              height="50"
              class="mb-4"
              @click="clearFilters"
          ></v-btn>
        </div>

      </v-card-actions>
    </v-card>
  </v-overlay>
</template>

<script setup>
const props = defineProps(['modelValue'])
const emit = defineEmits('close', 'apply')
const clearFilters = () => {
  emit('close')
};

const applyFilters = () => {
  emit('apply')
};
</script>

<style lang="scss">
.filter-footer {
  height: 100px;
  &-content {
    position: fixed;
    left:10px;
    right: 20px;
    bottom: 0px;
    background: white;
  }
}
</style>
