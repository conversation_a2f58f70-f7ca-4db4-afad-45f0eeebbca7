<template>
  <div class="header-menu desktop-only">
    <div class="header-menu-item" v-for="item in items">
      <div class="name" @click="gotoCategories">{{item}}</div>
<!--      <div class="content">-->
<!--        <v-row>-->
<!--          <v-col><v-list :items="items"></v-list></v-col>-->
<!--          <v-col><v-list :items="items"></v-list></v-col>-->
<!--          <v-col><v-list :items="items"></v-list></v-col>-->
<!--          <v-col><v-list :items="items"></v-list></v-col>-->
<!--          <v-col>-->
<!--            <v-img width="140" height="200" cover src="https://cdn.vuetifyjs.com/images/parallax/material.jpg" />-->
<!--            <v-btn class="mt-2" width="140" color="red" rounded="0">promo1</v-btn>-->
<!--          </v-col>-->
<!--          <v-col>-->
<!--            <v-img width="140" height="200" cover src="https://cdn.vuetifyjs.com/images/parallax/material.jpg" />-->
<!--            <v-btn class="mt-2" width="140" color="red" rounded="0">promo1</v-btn>-->
<!--          </v-col>-->
<!--        </v-row>-->
<!--      </div>-->
    </div>
  </div>
</template>
<script setup>
import { ref, readonly } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const tab = ref('item4')
const items = [
  'Home',
  'Shop',
  'Our story',
  'Blog',
  'Contact us',
  'Blog',
  'Blog'
]
const handleHover = (item) => {
  tab.value = item
}
const gotoCategories = () => {
  router.push('/categories')
}
</script>

<style lang="scss" scoped>
@use '~/assets/styles/default' as *;
.header-menu {
  position: relative;
  max-width: 1100px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  &-item {
    flex: 1;
    .name {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 5px 10px;
      line-height: 18px;
      height: 45px;
      cursor: pointer;
    }
    .content {
      display: none;
      position: absolute;
      top: 45px;
      left: 0px;
      right: 0px;
      z-index: 1;
      border-top: 1px solid #eaeaea;
      background: white;
      padding: 24px 10px;
    }
    &:hover {
      .name {
        color: white;
        background: black;
      }
      .content {
        display: block;
      }
    }
  }
}
</style>
