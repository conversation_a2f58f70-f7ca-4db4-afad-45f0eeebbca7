<template>
  <div class="web-share">
    <v-btn variant="text" icon="mdi-share-variant-outline" @click="handleShare"></v-btn>
    <input ref="input" readonly="readonly" type="text" class="web-share--input" v-model="url">
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const props = defineProps({
  title: {
    type: String,
    default: "",
  },
})
const url = ref('');

const handleShare = () => {
  const isSupported = !!(typeof window !== 'undefined' && navigator.share);

  if (!url.value) {
    url.value = location.href;
  }

  if (isSupported) {
    navigator.share({
      title: props.title,
      text: document.title,
      url: url.value
    });
  } else {
    handleCopy();
  }
}
const handleCopy = () => {
  const el = this.$refs.input;

  el.select();
  el.setSelectionRange(0, this.url.length);
  const status = document.execCommand('Copy');

  if (status) {
    window.sendUiNotification({
      icon: 'check',
      type: 'success',
      message: this.$t('Product link copied successfully')
    });
  } else {
    window.sendUiNotification({
      icon: 'error',
      type: 'danger',
      message: this.$t('Link copy failed')
    });
  }
}

onMounted(() => {
  url.value = location.href;

});
</script>

<style lang="scss" scoped>
.web-share {
  &--input {
    position: fixed;
    top: -1000px;
    left: -1000px;
    width: 100px;
  }
}
</style>
