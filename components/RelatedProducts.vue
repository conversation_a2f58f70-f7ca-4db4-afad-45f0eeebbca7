<template>
  <div class="related-products mb-12">
    <div class="title">{{props.title}}</div>
    <v-slide-group
        v-if="props.data?.length > 0"
        selected-class="bg-success"
        show-arrows
    >
      <v-slide-group-item
          v-for="item in props.data"
          :key="item.id"
          v-slot="{ isSelected, toggle, selectedClass }"
      >
        <ProductItem :data="item" />
      </v-slide-group-item>
    </v-slide-group>
    <div class="py-12 text-center">
      No related products found!
    </div>
  </div>
</template>

<script setup>
import ProductItem from "./ProductItem.vue";

const props = defineProps({
  title: '',
  data: []
})

const getImgUrl = (item) => {
  return item.product?.images?.length > 0 ? 'https://test5.axel.market' + item.product.images[0]['original_url'] : '/assets/images/default.jpg'
}
</script>

<style lang="scss">
.title {
  font-size: 32px;
  margin-top: 24px;
  margin-bottom: 12px;
}
.related-product {
  display: flex;
  flex-direction: column;
  margin-right:2px;
  margin-bottom: 24px;
  img {
    background: var(--grey-100);
    border: 0px;
  }
}
.related-product:last-child {
  margin-right: 0px;
}
.v-slide-group__prev--disabled,
.v-slide-group__next--disabled {
  display: none;
}
</style>
