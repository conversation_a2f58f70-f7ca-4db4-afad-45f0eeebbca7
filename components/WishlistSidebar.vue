<template>
  <v-overlay v-model="props.modelValue" class="d-flex justify-end">
    <div class="side-modal" rounded="0">
      <div class="fully-justify">
        <h4 v-if="products.length > 0">{{$('Wishlist')}}</h4>
        <v-spacer />
        <v-btn icon="mdi-close" variant="text" @click="toggleCartSidebar"></v-btn>
      </div>
      <div class="sidebar-main sidebar-wishlist" v-if="products.length > 0">
        <v-list lines="3">
          <template  v-for="item in products">
          <v-list-item class="py-2" style="border-bottom: 1px solid grey;">
            <template v-slot:prepend>
              <img :src="item.image" width="80" height="80"/>
            </template>
            <v-list-item-title>{{ item.name }}</v-list-item-title>
            <v-list-item-subtitle>Product item code: P75748989B</v-list-item-subtitle>
            <v-list-item-title class="py-4">$98</v-list-item-title>
            <v-list-item-media>
              <div class="d-flex fully-justify">
                <a class="text-center">Remove</a>
                <v-spacer />
                <b>View all detail</b>
                <v-btn class="ml-4" variant="elevated" rounded="0" color="black">Add to card</v-btn>
              </div>
            </v-list-item-media>
          </v-list-item>
          </template>
        </v-list>
      </div>
      <div class="sidebar-main text-center" v-else>
        <img src="~/assets/images/cart.svg" width="180" />
        <h2 class="mt-4">{{$t('Your Wishlist is empty')}}</h2>
        <p class="mt-4">Today is the best day for shopping</p>
      </div>
      <div class="sidebar-footer" v-if="products.length > 0">
        <h3> 12 added to Wishlist</h3>
        <div class="fully-justify">
          <v-btn
              variant="flat"
              size="large"
              :text="$t('Clean all')"
              height="50"
              rounded="0"
              @click="toggleCartSidebar"
          ></v-btn>
          <v-btn
              variant="flat"
              size="large"
              :text="$t('View full list')"
              height="50"
              rounded="0"
              @click="toggleCartSidebar"
          ></v-btn>
          <v-btn
              variant="flat"
              size="large"
              :text="$t('Add all to ')"
              color="black"
              height="50"
              rounded="0"
              @click="toggleCartSidebar"
          ></v-btn>
        </div>
      </div>
      <div class="sidebar-footer" v-else>
        <h3 class="my-4">Recently viewed </h3>
        <v-row>
          <v-col  v-for="product in relatedProducts">
            <img :src="product.image" width="120" />
            <p>{{product.name}}</p>
            <b>{{product.price}}</b>
          </v-col>
        </v-row>
        <v-btn
            variant="flat"
            size="large"
            :text="$t('Go shopping')"
            color="black"
            width="100%"
            height="50"
            rounded="0"
            @click="handleShopping"
        ></v-btn>
      </div>
    </div>
  </v-overlay>
</template>
<script setup>
import { watch } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const props = defineProps(['modelValue'])
const emit = defineEmits(['update:modelValue'])

const wiseStore = useWishlist()

const products = ref([]);
const relatedProducts = [{
  id: '1',
  name: 'Mounja Burn Drops For Weight LossMounja Burn Drops For Weight Loss',
  price: '$27',
  image: 'https://market.viacube.com/rails/active_storage/representations/proxy/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBBcjhkIiwiZXhwIjpudWxsLCJwdXIiOiJibG9iX2lkIn19--3d6a1262046c28015fa3b1f29a12028cb7825ff2/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdDRG9MWm05eWJXRjBTU0lJYW5CbkJqb0dSVlE2RTNKbGMybDZaVjloYm1SZmNHRmtXd2hwQWZCcEFmQjdCam9NWjNKaGRtbDBlVWtpQzJObGJuUmxjZ1k3QmxRNkNuTmhkbVZ5ZXdZNkRIRjFZV3hwZEhscFZRPT0iLCJleHAiOm51bGwsInB1ciI6InZhcmlhdGlvbiJ9fQ==--4b059456da4e83d2cba81b5a78c7a579ac51ee20/Untitled-1.jpg'
}, {
  id: '2',
  name: 'Mounja Burn Drops For Weight Loss',
  price: '$25',
  image: 'https://market.viacube.com/rails/active_storage/representations/proxy/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBBcjhkIiwiZXhwIjpudWxsLCJwdXIiOiJibG9iX2lkIn19--3d6a1262046c28015fa3b1f29a12028cb7825ff2/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdDRG9MWm05eWJXRjBTU0lJYW5CbkJqb0dSVlE2RTNKbGMybDZaVjloYm1SZmNHRmtXd2hwQWZCcEFmQjdCam9NWjNKaGRtbDBlVWtpQzJObGJuUmxjZ1k3QmxRNkNuTmhkbVZ5ZXdZNkRIRjFZV3hwZEhscFZRPT0iLCJleHAiOm51bGwsInB1ciI6InZhcmlhdGlvbiJ9fQ==--4b059456da4e83d2cba81b5a78c7a579ac51ee20/Untitled-1.jpg'
}, {
  id: '3',
  name: 'Mounja Burn Drops',
  price: '$27',
  image: 'https://market.viacube.com/rails/active_storage/representations/proxy/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaHBBcjhkIiwiZXhwIjpudWxsLCJwdXIiOiJibG9iX2lkIn19--3d6a1262046c28015fa3b1f29a12028cb7825ff2/eyJfcmFpbHMiOnsibWVzc2FnZSI6IkJBaDdDRG9MWm05eWJXRjBTU0lJYW5CbkJqb0dSVlE2RTNKbGMybDZaVjloYm1SZmNHRmtXd2hwQWZCcEFmQjdCam9NWjNKaGRtbDBlVWtpQzJObGJuUmxjZ1k3QmxRNkNuTmhkbVZ5ZXdZNkRIRjFZV3hwZEhscFZRPT0iLCJleHAiOm51bGwsInB1ciI6InZhcmlhdGlvbiJ9fQ==--4b059456da4e83d2cba81b5a78c7a579ac51ee20/Untitled-1.jpg'
}];
const toggleCartSidebar = () => {
  emit('update:modelValue', false)
}
const handleShopping = () => {
  router.push('/categories')
}
watch(
    () => props.modelValue,
    (value) => {
      if (value) {
        wiseStore.getList().then(data => {
          products.value = data
        })
      }
    })
</script>

<style lang="scss" scoped>
.sidebar-wishlist {
  border-top: 2px solid #000;
}
.sidebar-main {
  margin-bottom: 80px;
}
.sidebar-footer {
  position: fixed;
  bottom: 10px;
  left: 0px;
  right: 24px;
  padding: 0 16px;
  background: white;
}
</style>
