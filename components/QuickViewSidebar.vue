<template>
  <v-overlay id="quick-view" v-model="props.modelValue" class="d-flex justify-end">
    <div class="d-flex bg-white">
      <div class="quick-view-images">
        <template v-if="selectedVarient?.images?.length" v-for="item in selectedVarient.images">
          <img :src="config.public.targetUrl + item.original_url" width="400" />
        </template>
        <template v-else>
          <img :src="config.app.buildAssetsDir + 'assets/images/default.jpg'" width="400" />
        </template>
      </div>
      <ProductDetail width="400" :data="props.data">
        <template #header>
          <v-card-title class="d-flex">
            <v-icon icon="mdi-heart-outline" variant="text" />
            <v-spacer />
            <v-icon icon="mdi-close" @click="handleClose"/>
          </v-card-title>
        </template>
        <template #more>
          <v-card-item>
            <p class="mt-8"><a class="hover" @click="viewFullDetails">View Full Details </a></p>
          </v-card-item>
        </template>
      </ProductDetail>

    </div>
  </v-overlay>
</template>

<script setup>
import { useRouter } from 'vue-router'
import {computed} from "vue";

const router = useRouter()
const config = useRuntimeConfig()
const props = defineProps(['modelValue', 'data'])
const emit = defineEmits(['update:modelValue'])

const selectedVarient = computed(() => {
  return props.data.selected_variants.find(item => item.id == props.data.default_variant_id)
})

const viewFullDetails = () => {
  router.push({name: 'product', params: {id: props.data.id}})
}

const handleClose = () => {
  emit('update:modelValue', false)
}

</script>

<style lang="scss">
.quick-view-images {
  width: 400px;
  height: 100vh;
  overflow: auto;
}

</style>
