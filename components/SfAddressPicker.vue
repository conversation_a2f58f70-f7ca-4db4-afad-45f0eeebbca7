<template>
  <div class="sf-address-picker">
    <v-row>
      <v-col class="fully-justify-v">
        <div class="item">
          <label>To whom</label>
          <p><PERSON><PERSON><PERSON><PERSON></p>
        </div>
        <div class="item">
          <label>Used for billing </label>
        </div>
      </v-col>
      <v-col>
        <div class="item">
          <label>Shipping Address</label>
          <p>4517 Washington Ave. Manchester, Kentucky 39495</p>
        </div>
        <div class="item">
          <label>Delivery instruction</label>
          <p>Please leave the package by the door.</p>
        </div>
      </v-col>
    </v-row>
    <div class="actions">
      <v-btn variant="text" class="text-decoration-underline text-capitalize">Change Address</v-btn>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.sf-address-picker {
  padding: 16px;
  background: var(--grey-100);
  line-height: 24px;

  label {
    display: block;
    font-weight: bold;
  }
  label, p {
    min-height: 24px;
    margin-bottom: 8px;
  }
  .actions {
    display: flex;
    justify-content: right;
    .v-btn {
      letter-spacing: normal;
    }
  }
}
</style>
