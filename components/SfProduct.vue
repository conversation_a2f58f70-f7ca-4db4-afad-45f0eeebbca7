<template>
  <div>
    <div class="product">
      <div class="product-image">
        <v-img :src="props.data.src" />
      </div>
      <div class="product-desc">
        <div class="title">{{props.data.name}}</div>
        <div v-if="props.type == 'edit'" class="attr">{{props.data.attr}}</div>
        <div class="quantity">Quantity {{props.data.quantity}}</div>
        <div v-if="props.type == 'edit'" class="upfront">$ 20.99 every week, all shipped upfront</div>
      </div>
      <div class="product-actions">
        <span class="product-price">{{props.data.price}}</span>
        <b variant="text" class="text-decoration-underline cursor-pointer" v-if="props.type == 'edit'" @click="handleEdit">Edit</b>
      </div>
    </div>
  </div>
</template>

<script setup>
const emit = defineEmits(['edit'])
const props = defineProps({
  type: {
    type: String,
    default: 'edit'
  },
  data: {
    type: Object,
    default: () => {
      return {
        name: 'Fresh product A ( variant)',
        attr: 'Product item code: P75748989B',
        quantity: '12',
        src: 'https://testmart.axel.market/images/default.jpg',
        price: '$80'
      }
    }
  }
})

const handleEdit = () => {
  emit('edit', props.data)
}
</script>

<style lang="scss" scoped>
.product {
  display: flex;
  justify-content: space-between;
  padding: 16px 0;
  border-bottom: 1px solid #EAEAEA;
  .product-image {
    flex: 0 0 80px;
    width: 80px;
    height: 80px;
  }
  .product-desc {
    flex: 1;
    padding-left: 12px;
  }
  .attr, .quantity {
    color: grey;
    font-size: 14px;
  }
  .upfront {
    font-size: 12px;
    font-weight: bold;
  }
  .product-actions {
    flex: 0 0 80px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: flex-end;
  }
}
</style>
