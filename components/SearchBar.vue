<template>
  <v-text-field
      class="search-bar"
      ref="searchBarRef"
      v-model="term"
      append-inner-icon="mdi-magnify"
      density="compact"
      clearable
      hide-details
      variant="outlined"
      height="40"
      rounded="0"
      :placeholder="$t('Search for items')"
      @keyup.enter="handleSearch"
      @focus="visible = true"
      @keydown.esc="closeSearch"
  ></v-text-field>

  <v-overlay class="search-overlay" v-model="visible">
    <v-sheet class="search-container" v-click-outside="{
        handler: handleClickResultOutside
    }">
      <div class="main" v-if="result && result.length > 0">
        <label class="ml-8 font-size-16 text-uppercase">{{$t('Product suggestions')}}</label>
        <v-virtual-scroll
            class="results-listing"
            max-height="500"
            :items="result"
        >
          <template v-slot:default="{ item }">
            <ProductCard :data="item"/>
          </template>
        </v-virtual-scroll>
      </div>
      <v-empty-state
          v-else
          class="text-h6 text-center text-grey-darken-2"
      >
        <img src="~/assets/images/error/error.svg" width="300"/>
        <template v-if="term">
          <p class="mt-4">{{$t('We haven’t found any results for given phrase')}}</p>
        </template>
        <template v-else>
          <p class="mt-4">{{$t('You haven’t searched for items yet')}}</p>
          <p class="mt-4">{{$t('Let’s start now – we’ll help you')}}</p>
        </template>
      </v-empty-state>
    </v-sheet>
  </v-overlay>
</template>

<script setup>
import {ref} from 'vue';

const productStore = useProduct();
const searchBarRef = ref()
const visible = ref(false)
const result = ref([])
const term = ref('')

const handleOpen = (type) => {
  visible.value = true
}
const handleSearch = async () => {
  result.value = await productStore.getProducts({
    name: term.value
  })
}
const closeSearch = () => {
  visible.value = false;
}

const handleClickResultOutside = (e) => {
  if (!searchBarRef.value.$el.contains(e.target)) {
    closeSearch()
  }
}
</script>

<style lang="scss" scoped>
.search-overlay {
  z-index: 1 !important;
  .v-overlay__content {
    width: 100% !important;
  }
}

::v-deep(.search-container) {
  margin-top: 60px;
  padding-top: 24px;
  width: 100vw;
  background: white;
  .v-empty-state {
    .v-responsive {
      height: 300px !important;
    }
  }
}

::v-deep(.results-listing ) {
  .v-virtual-scroll__container {
    display: flex;
    flex-wrap: wrap;
  }
  .v-virtual-scroll__item {
    width: 25%;
  }
}
</style>
