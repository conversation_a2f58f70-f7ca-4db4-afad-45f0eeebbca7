<template>
  <div class="reset-success" v-if="reset">
    <div class="title">You are all set for sign in!</div>
    <p class="my-8">
      Success! Your password has been updated successfully. Remember to keep it
      safe and never share it with anyone.
    </p>
    <v-btn color="black" height="52" rounded="0" block>{{
      $t("Sign in")
    }}</v-btn>
  </div>
  <div class="resetpassword" v-else>
    <h2 class="py-8">Welcome back, volodymyr lazarev!</h2>
    <SfAlert
      class="mb-8"
      icon="mdi-shield-account"
      message="Almost there! You're just a step away from completing your account setup. To ensure the safety of your account, please create a strong and secure password. Take a moment to choose a password that's easy for you to remember but difficult for others to guess. "
    />
    <div class="mb-8">
      <p>Enter the password you would like to use with your account</p>
      <b><EMAIL></b>
    </div>
    <v-form fast-fail @submit.prevent="handleSubmit">
      <v-text-field
        v-model="formData.username"
        :rules="[rules.required]"
        class="mb-2"
        :label="$t('Your email')"
        variant="outlined"
        clearable
        active
        required
      ></v-text-field>

      <v-text-field
        v-model="formData.password"
        :rules="[rules.required]"
        :append-inner-icon="pwdType ? 'mdi-eye' : 'mdi-eye-off'"
        :type="pwdType ? 'text' : 'password'"
        @click:append-inner="pwdType = !pwdType"
        class="mb-2"
        :label="$t('Password')"
        variant="outlined"
        clearable
        active
        required
      ></v-text-field>

      <v-btn
        class="mt-2"
        color="black"
        size="large"
        rounded="0"
        type="submit"
        block
        >{{ $t("Set new password") }}</v-btn
      >
    </v-form>
  </div>
</template>

<script setup>
import { reactive, readonly, ref } from "vue";

const rules = readonly({
  required: (value) => {
    if (!value) {
      return "This field is required";
    }
  },
});
const reset = ref(false);
const pwdType = ref(true);
const formData = reactive({
  username: "",
  password: "",
  remember_me: false,
});
const handleSubmit = async (evt) => {
  const { valid } = await evt.then();

  if (!valid) {
    return false;
  }
  reset.value = true;
};
</script>

<style lang="scss">
.resetpassword {
  max-width: 650px;
  min-height: 450px;
  margin: 84px auto;
  padding: 0 16px;
}
.reset-success {
  max-width: 560px;
  margin: 84px auto;
  padding: 32px 64px;
  border: 1px solid var(--grey-500);
  .title {
    font-size: 32px;
  }
}
</style>
