<template>
  <div class="main">
    <v-breadcrumbs
      class="px-0"
      divider="/"
      :items="breadcrumbs"
    ></v-breadcrumbs>
    <client-only>
      <CategoryPageHeader :total="totalCount" @clear="handleClear" />
    </client-only>
    <div class="products">
      <div class="product-loading" v-if="loading">
        <v-progress-circular indeterminate></v-progress-circular>
      </div>
      <template v-else v-for="item in products">
        <ProductCard
          :data="item"
          class="product"
          @view="handleView"
          @cart="handleCart"
          @wish="handleWish"
        >
        </ProductCard>
      </template>
    </div>
    <div class="products__footer">
      <v-pagination
        v-model="page"
        :length="totalPage"
        :total-visible="6"
        prev-icon="mdi-arrow-left"
        next-icon="mdi-arrow-right"
        @update:modelValue="productStore.changePage"
      ></v-pagination>
      <div class="d-flex align-center">
        <span>Rows per page:</span>
        <v-select
          v-model="pageSize"
          width="90"
          :items="[10, 20, 40]"
          bg-color="white"
          density="compact"
          hide-details
          @update:modelValue="productStore.changePageSize"
        ></v-select>
      </div>
    </div>
  </div>

  <QuickViewSidebar v-model="quickViewFlag" :data="quickViewItem" />
</template>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
const productStore = useProduct();
const wishStore = useWishlist();
const cartStore = useCartlist();
const {
  loading,
  page,
  pageSize,
  totalPage,
  totalCount,
  products = [],
} = storeToRefs(productStore);

const quickViewFlag = ref(false);
const quickViewItem = ref(false);

const breadcrumbs = computed(() => {
  return [
    {
      href: "/",
      title: "Home",
    },
    {
      href: "/categories",
      title: "Categories",
    },
  ];
});

const handleView = (data) => {
  productStore.getProduct(data.id).then((product) => {
    quickViewFlag.value = true;
    quickViewItem.value = product;
  });
};
const handleCart = (data) => {
  console.log(data);
  cartStore.addItem({
    variant_id: data.default_variant_id,
    listing_id: data.id,
    quantity: 1,
  });
};
const handleWish = (data) => {
  wishStore.addItem({
    variant_id: data.default_variant_id,
    listing_id: data.id,
    quantity: 1,
  });
};
const handleClear = () => {
  getList();
};
const getList = () => {
  productStore.getProducts();
};
getList();
</script>

<style lang="scss" scoped>
@use "~/assets/styles/default" as *;
.main {
  @include for-mobile {
    padding: 0 24px;
  }
}
.products {
  display: flex;
  flex-wrap: wrap;
  min-height: 200px;
  margin-right: -16px;
  margin-left: -16px;
}
.product {
  width: 25%;
}
.product-loading {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: center;
}
.products__footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  .v-pagination__item {
    button {
      width: 20px;
    }
    &--is-active {
    }
  }
}
</style>
