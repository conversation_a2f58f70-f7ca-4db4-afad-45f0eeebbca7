<template>
  <div id="product" class="main">
    <v-breadcrumbs
      class="px-0"
      divider="/"
      :items="['Home', 'Categories', 'Gifting']"
    ></v-breadcrumbs>
    <div class="product">
      <SfGallery :data="galleryImages" />
      <div class="product__info">
        <ProductDetail :data="product">
          <template #header>
            <v-card-title class="d-flex justify-right">
              <v-icon icon="mdi-heart-outline" variant="text" />
              <WebShare :product="product" />
            </v-card-title>
          </template>
        </ProductDetail>
        <div class="product__desc desktop-only">
          <v-tabs v-model="tab" fixed-tabs>
            <v-tab :value="1">{{ $t("Description") }}</v-tab>
            <v-tab :value="2">{{ $t("Product details") }}</v-tab>
          </v-tabs>
          <v-tabs-window v-model="tab">
            <v-tabs-window-item>
              <div class="py-8" v-html="product.description"></div>
            </v-tabs-window-item>
            <v-tabs-window-item>
              <div class="py-8" v-html="product.description"></div>
            </v-tabs-window-item>
          </v-tabs-window>
        </div>
        <div class="product__desc smartphone-only mt-4">
          <v-expansion-panels border="0">
            <v-expansion-panel
              v-for="(item, index) in [
                $t('Description'),
                $t('Product details'),
              ]"
              :key="index"
              text="Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat."
              :title="item"
            ></v-expansion-panel>
          </v-expansion-panels>
        </div>
      </div>
    </div>
    <RelatedProducts
      :data="product.matched_listings"
      :title="$t('Related poduct')"
    />
  </div>
</template>

<script setup>
import { ref } from "vue";
import { useRoute } from "vue-router";

const route = useRoute();
const productStore = useProduct();
const tab = ref(1);
const relatedProducts = ref([]);
const product = ref({});
const selectedVarient = ref({});
const galleryImages = ref([]);

productStore.getProduct(route.params.id).then((data) => {
  product.value = data;
  selectedVarient.value = data.selected_variants.find(
    (item) => item.id == data.default_variant_id,
  );
  console.log("abc", selectedVarient.value.images.length);
  galleryImages.value = selectedVarient.value.images.length
    ? selectedVarient.value.images
    : data.product.images;
  console.log(galleryImages.value);
});
</script>

<style lang="scss" scoped>
@use "~/assets/styles/default" as *;
.product {
  display: flex;
  @include for-mobile {
    display: block;
  }
  &__info {
    flex: 1;
    margin: 0 0 0 48px;
    @include for-mobile {
      margin-left: 0px;
    }
    .v-slide-group__content {
      border-bottom: 2px solid #eee;
      justify-content: space-between;
      .v-btn {
        padding: 0px;
        text-transform: capitalize;
        color: #72757e;
        font-size: 1.125rem;
        letter-spacing: normal;
        &:hover {
          background: transparent;
        }
      }
      .v-tab--selected {
        color: black;
      }
      .v-btn__overlay {
        background: transparent;
      }
    }
  }
  &__price-and-rating {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: var(--spacer-sm) 0 var(--spacer-lg) 0;
  }
  &__notify-me {
    margin: var(--spacer-sm) 0 var(--spacer-sm);
  }
  .buy-button {
    display: flex;
    margin-left: 180px;
  }
}
</style>
