<template>
  <h1 class="py-4 mt-4 text-center" v-if="isThankYou">Thank You!</h1>
  <div id="checkout" class="checkout">
    <div class="checkout__main" v-if="isThankYou">
      <ThankYou />
    </div>
    <div class="checkout__main" v-else>
      <h3>Checkout</h3>
      <Shipping :step="activeStep" @update="handleStep" />
      <ShippingMethod :step="activeStep" @update="handleStep" />
      <Payment :step="activeStep" />
    </div>
    <div class="checkout__aside">
      <CartPreview />
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import CartPreview from "~/components/checkout/CartPreview";
import Shipping from "~/components/checkout/Shipping";
import ShippingMethod from "~/components/checkout/ShippingMethod";
import Payment from "~/components/checkout/Payment";
import ThankYou from "~/components/checkout/ThankYou";

const route = useRoute();
const isThankYou = ref(route.name.includes("thankyou"));
const activeStep = ref(1);
const handleStep = () => {
  console.log("handle step");
  activeStep.value++;
};
</script>

<style lang="scss">
@use "~/assets/styles/default" as *;
.checkout {
  max-width: 1200px;
  margin: var(--spacer-base) auto;
  display: flex;
  @include for-mobile {
    display: block;
    width: 100%;
    padding: 0 24px;
  }
  &__main {
    flex: 1;
  }
  &__aside {
    flex: 0 0 25.5rem;
    margin: 0 0 0 4.25rem;
    @include for-mobile {
      margin: 0px;
      padding: 0px;
    }
  }
  &__title {
    border-bottom: 1px solid var(--grey-500);
    margin: var(--spacer-sm) 0;
    height: 48px;
    line-height: 48px;
    padding-left: var(--spacer-xs);
  }
  &__sub-title {
    height: 28px;
    line-height: 28px;
    margin: var(--spacer-sm) 0;
  }
  .panel {
    padding: 16px;
    background: var(--grey-100);
    line-height: 24px;

    label {
      display: block;
      font-weight: bold;
    }
    label,
    p {
      min-height: 24px;
    }
    .actions {
      display: flex;
      justify-content: right;
      .v-btn {
        letter-spacing: normal;
      }
    }
  }
  .v-field {
    .v-field__field {
      height: 52px;
    }
    .v-field__overlay {
      border-radius: 4px;
    }
    .v-field__outline {
      display: none;
    }
  }
  .v-radio {
    height: 52px;
    line-height: 52px;
    background: var(--grey-100);
    margin-bottom: var(--spacer-xs);
    border: 1px solid #fff;
    .v-selection-control--dirty {
      border: 1px solid var(--grey-500);
    }
    .v-label {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
    }
  }
  &-dialog {
    .v-field {
      .v-field__field {
        height: 52px;
      }
      .v-field__overlay {
        border-radius: 4px;
      }
      .v-field__outline {
        display: none;
      }
    }
    .v-radio {
      height: 52px;
      margin-bottom: var(--spacer-xs);
      border: 1px solid #fff;
      .v-selection-control--dirty {
        border: 1px solid var(--grey-500);
      }
      .v-label {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
      }
    }
  }
}
</style>
