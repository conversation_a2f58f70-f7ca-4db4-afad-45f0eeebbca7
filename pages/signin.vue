<template>
  <div class="signin">
    <h2 class="py-8">Sign in to your account!</h2>
    <v-form fast-fail @submit.prevent="login">
      <v-text-field
        v-model="formData.username"
        :rules="[rules.required]"
        class="mb-2"
        :label="$t('Your email')"
        variant="outlined"
        clearable
        active
        required
      ></v-text-field>

      <v-text-field
        v-model="formData.password"
        :rules="[rules.required]"
        :append-inner-icon="pwdType ? 'mdi-eye' : 'mdi-eye-off'"
        :type="pwdType ? 'text' : 'password'"
        @click:append-inner="pwdType = !pwdType"
        class="mb-2"
        :label="$t('Password')"
        variant="outlined"
        clearable
        active
        required
      ></v-text-field>

      <div class="fully-justify">
        <v-checkbox
          v-model="formData.remember_me"
          label="Keep me signed in"
          hide-details
        ></v-checkbox>
        <nuxt-link to="/reset-password"
          ><b class="text-decoration-underline">{{
            $t("Forgotten password?")
          }}</b></nuxt-link
        >
      </div>
      <v-btn
        class="mt-2"
        color="black"
        size="large"
        rounded="0"
        :disabled="loading"
        v-loading="loading"
        type="submit"
        block
      >
        {{ $t("Sign in") }}</v-btn
      >
    </v-form>
    <div class="my-6 text-decoration-underline text-center cursor-pointer">
      <b @click="gotoSignup">CREATE ACCOUNT</b>
    </div>
  </div>
  <div class="infos"></div>
</template>

<script setup>
import { inject, reactive, readonly, ref } from "vue";
import { useRouter } from "vue-router";
const $notify = inject("notify");

const { logIn } = useUser();
const router = useRouter();
const rules = readonly({
  required: (value) => {
    if (!value) {
      return "This field is required";
    }
  },
});
const loading = ref(false);
const pwdType = ref(true);
const formData = reactive({
  username: "",
  password: "",
  remember_me: false,
});
const login = async (evt) => {
  const { valid } = await evt.then();

  if (!valid) {
    return false;
  }
  loading.value = true;
  logIn(formData)
    .then((data) => {
      $notify({
        type: "success",
        message: "Login success",
      });
      console.log(data);
      router.push("/my-account");
    })
    .catch((e) => {
      console.log(e);
      $notify({
        type: "error",
        message: e.error_description || "Login failure",
      });
    })
    .finally(() => {
      loading.value = false;
    });
};
const gotoSignup = () => {
  router.push("/signup");
};
</script>

<style lang="scss">
.signin {
  max-width: 560px;
  height: 450px;
  margin: 84px auto;
  padding: 0 16px;
}
</style>
