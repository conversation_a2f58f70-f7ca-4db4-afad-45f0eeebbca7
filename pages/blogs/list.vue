<template>
  <div class="blogs list">
    <v-breadcrumbs
      class="px-0"
      divider="/"
      :items="['Home', 'Blogs']"
    ></v-breadcrumbs>
    <div class="text-h4">Blogs</div>
    <div class="blogs-categories d-flex-start mt-3">
      <template v-for="item in blogsCategories" :key="item.id" class="d-flex">
        <v-btn
          flat
          size="large"
          :title="item.name"
          :class="{ 'bg-grey-darken-4': item.id === selectedId }"
          class="btn-normal-font"
          min-width="100"
          @click="handleSelectCategory(item)"
        >
          {{ item.name }}
        </v-btn>
      </template>
    </div>
    <div class="blogs-list">
      <div class="blogs-list-row" v-if="blogsList.length">
        <div class="blogs-list-item" v-for="item in blogsList" :key="item.id">
          <nuxt-link :to="localePath(`/blogs/${item.slug}`)">
            <div
              class="blogs-list-item-img"
              :style="{
                backgroundImage: `url(${config.public.targetUrl + item.feature_image})`,
              }"
            ></div>
            <div
              class="blogs-list-item-title text-line-2 fz-20"
              v-text="item.title"
            ></div>
            <div class="text-line-2" v-html="item.summary"></div>
          </nuxt-link>
        </div>
      </div>
    </div>
    <div class="list__footer my-12">
      <v-pagination
        v-model="page"
        class=""
        :length="totalPage"
        :total-visible="6"
        prev-icon="mdi-arrow-left"
        next-icon="mdi-arrow-right"
      ></v-pagination>
      <div class="d-flex align-center">
        <span class="text-grey-darken-1">{{ $t("Rows per page") }}:</span>
        <v-select
          v-model="pageSize"
          width="90"
          :items="[12, 18, 24, 36, 60]"
          variant="text"
          hide-details
          @update:modelValue="handleChange"
        ></v-select>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref } from "vue";
import { useLocalePath } from "#i18n";

const config = useRuntimeConfig();
const localePath = useLocalePath();
const blogStore = useBlog();
const { loading, page, pageSize, totalPage } = storeToRefs(blogStore);
const selectedId = ref("");
const blogsList = ref([]);
const blogsCategories = ref([]);

const handleChange = (value) => {
  console.log(value);
  blogStore.changePageSize(value);
};
const handleSelectCategory = ({ id }) => {
  selectedId.value = id;
  getList(id);
};
blogStore.getBlogCategories().then((data) => {
  data.unshift({
    id: "",
    name: "All",
  });
  blogsCategories.value = data;
});
const getList = (blogCategoryId) => {
  blogStore.getBlogs(blogCategoryId).then((data) => {
    blogsList.value = data;
  });
};
getList();
</script>
<style lang="scss" scoped>
.blogs {
  max-width: 1100px;
  margin: auto;
  :deep(.v-breadcrumbs-item) {
    padding-left: 0;
  }
  .text-line-2 {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  & &-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin: var(--spacer-lg) 0;
    &-row {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;
      width: 100%;
    }
    &-item {
      flex-basis: 32%;
      &:hover {
        & > a {
          text-decoration: underline;
        }
        .blogs-list-item-img {
          box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
      }
      color: #0a0a0a;
      & > a {
        overflow: hidden;
        color: #0a0a0a;
        text-decoration: none;
      }
      &-img {
        width: 100%;
        height: 460px;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
        transition: box-shadow 0.4s ease;
      }
      &-title {
        font-weight: 600;
        font-size: 48px;
        margin: var(--spacer-sm) 0;
      }
    }
  }
  .empty-data {
    padding: 16px;
    text-align: center;
    font-size: 24px;
    color: #666;
  }
  .btn-normal-font {
    text-transform: math-auto;
  }
  .list__footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .v-pagination__item {
      button {
        width: 20px;
      }
      &--is-active {
      }
    }
  }
}
@media (max-width: 1100px) {
  .blogs {
    padding: 0 12px;
  }
}
@media (max-width: 800px) {
  .blogs {
    & &-list {
      &-row {
        flex-direction: column;
      }
      &-item {
        flex-basis: 100%;
        &-title {
          font-size: 20px;
        }
        &-img {
          height: 200px;
        }
      }
    }
  }
}
</style>
