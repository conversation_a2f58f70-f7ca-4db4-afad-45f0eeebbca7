<template>
  <div class="blogs">
    <v-breadcrumbs
      class="px-0"
      divider="/"
      :items="breadcrumbs"
    ></v-breadcrumbs>
    <div class="text-h4">Blogs</div>
    <div class="blogs-categories d-flex-start mt-3">
      <template v-for="item in blogsCategories" :key="item.id" class="d-flex">
        <v-btn
          flat
          size="large"
          :title="item.name"
          :class="{ 'bg-grey-darken-4': item.id === selectedId }"
          class="btn-normal-font"
          min-width="100"
          @click="handleSelectCategory(item)"
        >
          {{ item.name }}
        </v-btn>
      </template>
    </div>
    <div class="blogs-list">
      <!-- firstRow -->
      <div class="blogs-list-row" v-if="firstRow">
        <div class="blogs-list-item" style="flex-basis: 100%">
          <nuxt-link :to="localePath(`/blogs/${firstRow.slug}`)">
            <div
              class="blogs-list-item-img"
              :style="{
                backgroundImage: `url(${config.public.targetUrl + firstRow.feature_image})`,
              }"
            ></div>
            <div
              class="blogs-list-item-title text-line-2"
              v-text="firstRow.title"
            ></div>
            <div class="text-line-2" v-html="firstRow.summary"></div>
          </nuxt-link>
        </div>
      </div>
      <div v-else class="empty-data" v-text="$t('No blogs found')"></div>
      <!-- secondRow -->
      <div class="blogs-list-row" v-if="secondRow.length">
        <div
          class="blogs-list-item"
          v-for="item in secondRow"
          :key="item.id"
          :style="getStyles(secondRow.length)"
        >
          <nuxt-link :to="localePath(`/blogs/${item.slug}`)">
            <div
              class="blogs-list-item-img"
              :style="{
                backgroundImage: `url(${config.public.targetUrl + item.feature_image})`,
              }"
            ></div>
            <div
              class="blogs-list-item-title text-line-2"
              :class="getClass(item.length)"
              v-text="item.title"
            ></div>
            <div class="text-line-2" v-html="item.summary"></div>
          </nuxt-link>
        </div>
      </div>
      <!-- thirdRow -->
      <div class="blogs-list-row" v-if="thirdRow.length">
        <div
          class="blogs-list-item"
          v-for="item in thirdRow"
          :key="item.id"
          :style="getStyles(thirdRow.length)"
        >
          <nuxt-link :to="localePath(`/blogs/${item.slug}`)">
            <div
              class="blogs-list-item-img"
              :style="{
                backgroundImage: `url(${config.public.targetUrl + item.feature_image})`,
              }"
            ></div>
            <div
              class="blogs-list-item-title text-line-2"
              :class="getClass(thirdRow.length)"
              v-text="item.title"
            ></div>
            <div class="text-line-2" v-html="item.summary"></div>
          </nuxt-link>
        </div>
      </div>
      <div class="d-flex justify-center mt-6" v-if="blogsList.length > 7">
        <v-btn
          flat
          size="large"
          class="bg-grey-darken-4 fz-14 view-all rounded-0 btn-normal-font"
          @click="goToList"
        >
          {{ $t("View all blogs") }}
        </v-btn>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { useLocalePath } from "#i18n";
import { useRouter } from "vue-router";

const config = useRuntimeConfig();
const blogStore = useBlog();
const {
  loading,
  page,
  pageSize,
  totalPage,
  totalCount,
  blogs = [],
  getBlogs,
} = storeToRefs(blogStore);

const router = useRouter();
const localePath = useLocalePath();
const selectedId = ref(1);
const blogsList = ref([]);
const blogsCategories = ref([]);
const breadcrumbs = ref([
  {
    href: "/",
    title: "Home",
  },
  {
    href: "/blogs",
    title: "Blogs",
  },
]);

const handleSelectCategory = ({ id }) => {
  selectedId.value = id;
  getList(id);
};
const firstRow = computed(() => {
  return blogsList.value[0];
});
const secondRow = computed(() => {
  return blogsList.value?.slice(1, 4);
});
const thirdRow = computed(() => {
  return blogsList.value?.slice(4, 7);
});
const goToList = () => {
  router.push("/blogs/list");
};
const getStyles = (count) => {
  if (count === 1) {
    return "flex-basis: 50%; margin: 0 auto;";
  } else if (count === 2) {
    return "flex-basis: 50%;";
  } else {
    return "flex-basis: 33.33%;";
  }
};
const getClass = (count) => {
  if (count === 1) {
    return "";
  } else if (count === 2) {
    return "fz-28";
  } else {
    return "fz-20";
  }
};
blogStore.getBlogCategories().then((data) => {
  data.unshift({
    id: "",
    name: "All",
  });
  blogsCategories.value = data;
});
const getList = (blogCategoryId) => {
  blogStore.getBlogs(blogCategoryId).then((data) => {
    console.log("blogstore blogs");
    blogsList.value = data;
  });
};
getList();
</script>
<style lang="scss" scoped>
.blogs {
  max-width: 1100px;
  margin: auto;
  :deep(.v-breadcrumbs-item) {
    padding-left: 0;
  }
  .text-line-2 {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-word;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  & &-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin: var(--spacer-lg) 0;
    &-row {
      display: flex;
      gap: 16px;
      width: 100%;
      &:nth-child(2) {
        .blogs-list--item {
          flex-grow: 1;
        }
      }
    }
    &-item {
      &:hover {
        & > a {
          text-decoration: underline;
        }
        .blogs-list-item-img {
          box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }
      }
      color: #0a0a0a;
      & > a {
        overflow: hidden;
        color: #0a0a0a;
        text-decoration: none;
      }
      &-img {
        width: 100%;
        height: 460px;
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
        transition: box-shadow 0.4s ease;
      }
      &-title {
        font-weight: 600;
        font-size: 48px;
        margin: var(--spacer-sm) 0;
      }
    }
  }
  .empty-data {
    padding: 16px;
    text-align: center;
    font-size: 24px;
    color: #666;
  }
  .view-all {
    width: 100%;
    max-width: 350px;
  }
  .btn-normal-font {
    text-transform: math-auto;
  }
}
@media (max-width: 1100px) {
  .blogs {
    padding: 0 12px;
  }
}
@media (max-width: 800px) {
  .blogs {
    & &-list {
      &-row {
        flex-direction: column;
      }
      &-item {
        flex-basis: 100%;
        &-title {
          font-size: 20px;
        }
        &-img {
          height: 200px;
        }
      }
    }
  }
}
</style>
