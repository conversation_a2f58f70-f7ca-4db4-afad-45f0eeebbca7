<template>
  <div class="blog mb-15">
    <v-breadcrumbs
      class="px-0"
      divider="/"
      :items="breadcrumbs"
    ></v-breadcrumbs>
    <div class="text-h4">Blogs</div>
    <template v-if="blog">
      <div class="blog-info">
        <div class="blog-title mt-6">
          <span v-text="blog.title"></span>
        </div>
        <div class="blog-actions">
          <SfButton
            v-if="!isGuest"
            class="sf-button--text"
            :disabled="loading"
            @click="handleToggleLike"
          >
            <SfImage
              :src="`/icons/heart${blog.is_liked ? '-fill' : ''}.svg`"
              width="24"
              height="24"
              alt="like blog"
            />
          </SfButton>
          <SfButton class="sf-button--text" @click="handleShare">
            <SfImage
              src="/icons/share.svg"
              width="24"
              height="24"
              alt="share blog"
            />
          </SfButton>
        </div>
      </div>
      <div
        class="blog-img rounded"
        :style="{
          backgroundImage: `url(${config.public.targetUrl + blog.feature_image})`,
        }"
      ></div>
      <div class="blog-detail" v-html="blog.description"></div>
    </template>
  </div>
</template>
<script setup>
import { computed, ref } from "vue";
import { useRouter, useRoute } from "vue-router";

const config = useRuntimeConfig();
const router = useRouter();
const route = useRoute();

const blogStore = useBlog();
const breadcrumbs = computed(() => {
  return [
    {
      href: "/",
      title: "Home",
    },
    {
      href: "/blogs",
      title: "Blogs",
    },
    {
      href: route.fullPath,
      title: blog.value?.title,
    },
  ];
});
const blog = ref();
const handleToggleLike = () => {
  const { slug } = route.params;
  const liked = blog.value?.is_liked;

  blogStore.updateBlogLike({ slug, like: liked }).then(() => {
    blog.value.is_liked = liked;
  });
};
const getDetail = () => {
  const { slug } = route.params;
  blogStore.getBlog(slug).then((data) => {
    blog.value = data;
  });
};

getDetail();
</script>
<style lang="scss" scoped>
.blog {
  color: #0a0a0a;
  min-height: 400px;
  margin: auto;
  max-width: 1100px;
  :deep(.v-breadcrumbs-item) {
    padding-left: 0;
  }
  & &-info {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin: 0 auto var(--spacer-base);
  }

  & &-title {
    flex: 1 1 80%;
    font-size: 48px;
    font-weight: 600;
  }
  & &-img {
    width: 100%;
    height: 460px;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    margin: var(--spacer-lg) 0;
  }
  & &-detail {
    font-size: 16px;
    font-weight: 400;
    line-height: 1.5;

    :deep(p) {
      margin: 0 0 12px;
    }
    :deep(img) {
      max-width: 100%;
    }
  }
}
@media (max-width: 1100px) {
  .blog {
    padding: 0 12px;
    & &-img {
      height: 230px;
    }
  }
}
</style>
