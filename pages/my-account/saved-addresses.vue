<template>
  <div class="pl-8 pr-8">
    <div v-if="!isEdit">
      <v-tabs v-model="tab" align-tabs="left" color="grey-darken-3">
        <v-tab value="savedAddresses">Saved addresses</v-tab>
      </v-tabs>
      <v-tabs-window v-model="tab">
        <v-tabs-window-item value="savedAddresses" class="pt-9">
          <div class="pb-9 under-line">
            Manage all the saved addresses you want (work place, home
            address...) This way you won"t have to enter an address manually
            with each order.
          </div>
          <div
            v-for="item in addresses"
            class="py-14 under-line d-flex justify-space-between align-center"
          >
            <div class="address-text">
              <div class="font-weight-bold">
                {{ item.firstname + " " + item.lastname }}
              </div>
              <div>{{ item.address1 }}</div>
              <div>{{ item.address2 }}</div>
              <div>
                {{ item.zipcode }}, {{ item.city }} {{ item.state_name }}
              </div>
              <div>{{ item.phone }}</div>
            </div>
            <div>
              <v-btn
                height="48"
                variant="tonal"
                class="mr-4 bg-grey-darken-3"
                @click="beforeEdit(item)"
              >
                Change
              </v-btn>
              <v-btn height="48" variant="tonal" @click="handleDelete(item.id)">
                Delete
              </v-btn>
            </div>
          </div>
          <v-btn
            height="48"
            variant="tonal"
            class="mr-4 bg-grey-darken-3 my-9"
            @click="isEdit = true"
          >
            Add new address
          </v-btn>
        </v-tabs-window-item>
      </v-tabs-window>
    </div>
    <div v-else>
      <v-tabs v-model="editTab" align-tabs="left" color="grey-darken-3">
        <v-tab value="savedAddresses">{{
          formData.id ? "Update the address" : "Add the address"
        }}</v-tab>
      </v-tabs>
      <v-tabs-window v-model="editTab">
        <v-tabs-window-item value="savedAddresses" class="pt-9">
          <div class="pb-9">
            Keep your addresses and contact details updated.
          </div>
          <v-form fast-fail @submit.prevent="handleEdit">
            <v-row>
              <v-col cols="12" sm="6">
                <v-text-field
                  v-model="formData.firstname"
                  label="First name"
                  variant="underlined"
                ></v-text-field>
              </v-col>

              <v-col cols="12" sm="6">
                <v-text-field
                  v-model="formData.lastname"
                  label="Last name"
                  variant="underlined"
                ></v-text-field>
              </v-col>
              <v-col cols="12" sm="12">
                <v-text-field
                  v-model="formData.address1"
                  label="Street address"
                  variant="underlined"
                ></v-text-field>
              </v-col>
              <v-col cols="12" sm="12">
                <v-text-field
                  v-model="formData.address2"
                  label="Apartment or suite number"
                  variant="underlined"
                ></v-text-field>
              </v-col>
              <v-col cols="12" sm="12">
                <v-select
                  v-model="formData.company"
                  label="Country"
                  :items="[
                    'California',
                    'Colorado',
                    'Florida',
                    'Georgia',
                    'Texas',
                    'Wyoming',
                  ]"
                  variant="underlined"
                ></v-select>
              </v-col>
              <v-col cols="12" sm="12">
                <v-text-field
                  v-model="formData.city"
                  label="City"
                  variant="underlined"
                ></v-text-field>
              </v-col>
              <v-col cols="12" sm="12">
                <v-text-field
                  v-model="formData.zipcode"
                  label="Zip-code"
                  variant="underlined"
                ></v-text-field>
              </v-col>
              <v-col cols="12" sm="12">
                <v-text-field
                  v-model="formData.phone"
                  label="Phone number"
                  variant="underlined"
                ></v-text-field>
              </v-col>
              <v-col cols="12" sm="12">
                <v-checkbox
                  v-model="formData.isDefault"
                  label="Set as default"
                ></v-checkbox>
              </v-col>
            </v-row>
            <v-btn
              height="48"
              variant="tonal"
              class="mr-4 bg-grey-darken-3 my-9"
              type="submit"
            >
              {{ formData.id ? "Update the address" : "Add the address" }}
            </v-btn>
          </v-form>
        </v-tabs-window-item>
      </v-tabs-window>
    </div>
  </div>
</template>

<script setup>
import { ref, inject, computed, reactive, readonly } from "vue";
const addressStore = useAddresses();
const { addresses } = storeToRefs(addressStore);

const tab = ref(null);
const editTab = ref(null);
const isEdit = ref(false);
const formData = ref({});

const beforeEdit = (data) => {
  isEdit.value = true;
  formData.value = data;
  console.log(data, "edit data");
};
const handleEdit = (evt) => {
  isEdit.value = false;
  addressStore.update(formData.value).then(() => {
    isEdit.value = false;
  });
};
const handleDelete = (id) => {
  addressStore.remove(id).then(() => {
    isEdit.value = false;
  });
};
addressStore.load();
</script>

<style lang="scss" scoped>
.under-line {
  border-bottom: 1px solid #f1f2f3;
}
.address-text {
  line-height: 40px;
}
</style>
