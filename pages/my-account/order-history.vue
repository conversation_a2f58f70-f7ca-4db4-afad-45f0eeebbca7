<template>
  <div class="pl-8 pr-8">
    <v-tabs v-model="tab" align-tabs="left" color="grey-darken-3">
      <v-tab value="MyOrders">My orders</v-tab>
    </v-tabs>
    <v-tabs-window v-model="tab">
      <v-tabs-window-item value="MyOrders" class="pt-9">
        <div class="mb-9">
          Feel free to edit any of your details below so your account is always
          up to date
        </div>
        <v-data-table :items="orders" :headers="headers" hide-default-footer>
          <template v-slot:item.invoice="{ item }">
            <div class="pt-3 cursor-pointer text-decoration-underline mb-2">
              {{ item.number }}
            </div>
            <div
              class="pb-3 cursor-pointer text-decoration-underline"
              @click="showOrderDetail(item.id)"
            >
              View details
            </div>
          </template>
        </v-data-table>
      </v-tabs-window-item>
    </v-tabs-window>
  </div>
</template>

<script setup>
import { ref, inject, computed, reactive, readonly } from "vue";
import { useRouter } from "vue-router";

const orderStore = useOrder();
const { orders } = storeToRefs(orderStore);

const router = useRouter();
const tab = ref(null);

const headers = reactive([
  { title: "Order ID", align: "start", key: "number", sortable: false },
  {
    title: "Payment date",
    align: "center",
    key: "created_at",
    width: 160,
    sortable: false,
  },
  {
    title: "Amount",
    align: "end",
    key: "display_item_total",
    width: 100,
    sortable: false,
  },
  { title: "Status", align: "end", key: "state", width: 100, sortable: false },
  {
    title: "Subscription",
    align: "end",
    key: "subscription",
    width: 100,
    sortable: false,
  },
  {
    title: "Invoice",
    align: "end",
    key: "invoice",
    width: 100,
    sortable: false,
  },
]);

const showOrderDetail = (id) => {
  router.push(`/my-account/order-details/${id}`);
};

orderStore.load();
</script>

<style lang="scss" scoped></style>
