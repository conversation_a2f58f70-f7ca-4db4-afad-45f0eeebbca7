<template>
  <div class="pl-8 pr-8">
    <v-tabs v-model="tab" align-tabs="left" color="grey-darken-3">
      <v-tab value="MyOrders">Order Details ({{ order.number }})</v-tab>
    </v-tabs>
    <v-tabs-window v-model="tab">
      <v-tabs-window-item value="MyOrders" class="pt-9">
        <div
          class="mb-9 cursor-pointer text-decoration-underline"
          @click="router.push(`/my-account/order-history`)"
        >
          Show All Orders
        </div>
        <v-card class="mx-auto">
          <v-card-text class="bg-surface-light pt-4">
            <div class="d-flex justify-space-between align-center mb-3">
              <span>Order ID:</span>
              <span>{{ order.number }}</span>
            </div>
            <div class="d-flex justify-space-between align-center mb-3">
              <span>Date :</span>
              <span>{{ order.created_at }}</span>
            </div>
            <div class="d-flex justify-space-between align-center mb-3">
              <span>Status:</span>
              <span>{{ order.state }}</span>
            </div>
            <div class="d-flex justify-space-between align-center mb-3">
              <span>Total :</span>
              <span>{{ order.display_item_total }}</span>
            </div>
          </v-card-text>
          <v-data-table
            :headers="headers"
            :items="order.line_items"
            hide-default-footer
            class="mt-9"
          >
          </v-data-table>
        </v-card>
      </v-tabs-window-item>
    </v-tabs-window>
  </div>
</template>

<script setup>
import { ref, inject, computed, reactive, readonly } from "vue";
import { useRoute, useRouter } from "vue-router";

const orderStore = useOrder();
const { order } = storeToRefs(orderStore);

const route = useRoute();
const router = useRouter();
const tab = ref(null);

const headers = reactive([
  { title: "Product", align: "start", key: "name", sortable: false },
  { title: "Quantity", align: "start", key: "quantity", sortable: false },
  { title: "Price", align: "start", key: "display_total", sortable: false },
]);
const consoles = ref([
  {
    Product: "test paid shipping with W= 0 | D=value",
    Quantity: "1",
    Price: "$100.00",
  },
]);

orderStore.getOrder(route.params.id);
</script>

<style lang="scss" scoped></style>
