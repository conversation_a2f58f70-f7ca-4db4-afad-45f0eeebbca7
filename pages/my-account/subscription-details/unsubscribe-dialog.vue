<template>
  <v-dialog v-model="dialog" width="500px">
    <v-card title="Skip delivery">
      <template v-slot:text>
        <div>Are you sure you want to cancel your subscription?</div>
      </template>

      <v-card-actions>
        <v-spacer></v-spacer>

        <v-btn
          class="text-none text-subtitle-1"
          color="black"
          size="large"
          variant="outlined"
          @click="dialog = false"
        >
          Cancel
        </v-btn>
        <v-btn
          class="text-none text-subtitle-1"
          color="black"
          size="large"
          variant="flat"
          @click="dialog = false"
        >
          Confirm
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, inject, computed, reactive, readonly } from "vue";

const dialog = defineModel();
</script>
