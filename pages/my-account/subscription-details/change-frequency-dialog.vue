<template>
  <v-dialog v-model="dialog" width="500px">
    <v-card title="Change frequency">
      <template v-slot:text>
        <v-radio-group v-model="dialog">
          <v-radio label="1 week(Current)" value="1 week(Current)"></v-radio>

          <v-radio label="2 weeks" value="2 weeks"></v-radio>

          <v-radio label="1 month" value="1 month"></v-radio>

          <v-radio label="2 months" value="2 months"></v-radio>

          <v-radio label="3 months" value="3 months"></v-radio>

          <v-radio label="6 months" value="6 months"></v-radio>
        </v-radio-group>
      </template>

      <v-card-actions>
        <v-spacer></v-spacer>

        <v-btn
          class="text-none text-subtitle-1"
          color="black"
          size="large"
          variant="outlined"
          @click="dialog = false"
        >
          Cancel
        </v-btn>
        <v-btn
          class="text-none text-subtitle-1"
          color="black"
          size="large"
          variant="flat"
          @click="dialog = false"
        >
          Confirm
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, inject, computed, reactive, readonly } from "vue";

const dialog = defineModel();
</script>
