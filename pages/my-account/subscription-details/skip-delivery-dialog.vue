<template>
  <v-dialog v-model="dialog" width="500px">
    <v-card title="Skip delivery">
      <template v-slot:text>
        <v-row>
          <v-col cols="12" sm="6">
            <div>Skip Delivery Date :</div>
            <div class="font-weight-bold">03/11/2025</div>
          </v-col>
          <v-col cols="12" sm="6">
            <div>Next Delivery Date :</div>
            <div class="font-weight-bold">03/09/2026</div>
          </v-col>
        </v-row>
      </template>

      <v-card-actions>
        <v-spacer></v-spacer>

        <v-btn
          class="text-none text-subtitle-1"
          color="black"
          size="large"
          variant="outlined"
          @click="dialog = false"
        >
          Cancel
        </v-btn>
        <v-btn
          class="text-none text-subtitle-1"
          color="black"
          size="large"
          variant="flat"
          @click="dialog = false"
        >
          Confirm
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, inject, computed, reactive, readonly } from "vue";

const dialog = defineModel();
</script>
