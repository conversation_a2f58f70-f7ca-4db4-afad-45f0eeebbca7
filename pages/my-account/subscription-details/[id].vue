<template>
  <div class="pl-8 pr-8">
    <v-tabs v-model="tab" align-tabs="left" color="grey-darken-3">
      <v-tab value="Subscription">Subscription details</v-tab>
    </v-tabs>
    <v-tabs-window v-model="tab">
      <v-tabs-window-item value="Subscription" class="pt-9">
        <v-card class="mx-auto">
          <v-card-text class="bg-surface-light pt-4">
            <v-row>
              <v-col cols="12" sm="2">
                <img src="../../../static/image/phone.png" />
              </v-col>
              <v-col cols="12" sm="6">
                <p>{{ subscription.name }}</p>
                <p>{{ subscription.variant }}</p>
              </v-col>
              <v-col cols="12" sm="2">
                {{ subscription.total_quantity }}
              </v-col>
              <v-col cols="12" sm="2">
                ${{ subscription.initial_price }}
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12" sm="3">
                <div>Start Date :</div>
                <div class="font-weight-bold">
                  {{ subscription.created_at }}
                </div>
              </v-col>
              <v-col cols="12" sm="3">
                <div>Next Delivery Date :</div>
                <div class="font-weight-bold">
                  <span>{{ subscription.next_delivery_date }}</span>
                  <span
                    class="cursor-pointer text-decoration-underline text-blue"
                    @click="isShowSkipDeliveryDialog = true"
                    >skip</span
                  >
                </div>
              </v-col>
              <v-col cols="12" sm="3">
                <div>Delivery every :</div>
                <div class="font-weight-bold">
                  <span>{{ subscription.delivery_every }}</span>
                  <span
                    class="cursor-pointer text-decoration-underline text-blue"
                    @click="isShowChangeFrequencyDialog = true"
                    >change</span
                  >
                </div>
              </v-col>
              <v-col cols="12" sm="3">
                <v-btn
                  height="48"
                  variant="tonal"
                  class="bg-white0"
                  @click="isShowUnsubscribeDialog = true"
                >
                  Unsubscribe
                </v-btn>
              </v-col>
            </v-row>
          </v-card-text>
          <v-data-table :items="orders" :headers="headers" hide-default-footer>
            <template v-slot:item.invoice="{ item }">
              <div class="cursor-pointer text-decoration-underline mb-2">
                {{ item.number }}
              </div>
            </template>
            <template v-slot:item.action="{ item }">
              <div
                class="cursor-pointer text-decoration-underline"
                @click="showOrderDetail(item.id)"
              >
                View details
              </div>
            </template>
          </v-data-table>
        </v-card>
      </v-tabs-window-item>
    </v-tabs-window>
  </div>

  <skip-delivery-dialog
    v-model="isShowSkipDeliveryDialog"
  ></skip-delivery-dialog>
  <change-frequency-dialog
    v-model="isShowChangeFrequencyDialog"
  ></change-frequency-dialog>
  <unsubscribe-dialog v-model="isShowUnsubscribeDialog"></unsubscribe-dialog>
</template>

<script setup>
import { ref, inject, computed, reactive, readonly } from "vue";
import { useRouter } from "vue-router";
import skipDeliveryDialog from "./skip-delivery-dialog.vue";
import changeFrequencyDialog from "./change-frequency-dialog.vue";
import unsubscribeDialog from "./unsubscribe-dialog.vue";

const subscripStore = useSubscription();
const orderStore = useOrder();
const { orders } = storeToRefs(orderStore);
const { subscription } = storeToRefs(subscripStore);

const router = useRouter();
const tab = ref(null);

const headers = reactive([
  { title: "Order ID", align: "start", key: "number", sortable: false },
  {
    title: "Payment date",
    align: "center",
    key: "created_at",
    width: 160,
    sortable: false,
  },
  {
    title: "Amount",
    align: "end",
    key: "display_item_total",
    width: 100,
    sortable: false,
  },
  { title: "Status", align: "end", key: "state", width: 100, sortable: false },
  {
    title: "Invoice",
    align: "end",
    key: "invoice",
    width: 100,
    sortable: false,
  },
  { title: "", align: "end", key: "action", width: 120, sortable: false },
]);
const isShowSkipDeliveryDialog = ref(false);
const isShowChangeFrequencyDialog = ref(false);
const isShowUnsubscribeDialog = ref(false);

const showOrderDetail = (id) => {
  router.push(`/my-account/order-details/${id}`);
};

subscripStore.getSubscribe();
orderStore.load();
</script>

<style lang="scss" scoped></style>
