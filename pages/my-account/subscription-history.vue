<template>
  <div class="pl-8 pr-8">
    <v-tabs v-model="tab" align-tabs="left" color="grey-darken-3">
      <v-tab value="Subscriptions">Subscriptions</v-tab>
    </v-tabs>
    <v-tabs-window v-model="tab">
      <v-tabs-window-item value="Subscriptions" class="pt-9">
        <v-data-table
          :items="subscriptions"
          hide-default-footer
          :headers="headers"
        >
          <template v-slot:item.amount="{ item }">
            <div class="d-flex justify-space-between">
              <div>${{ item.initial_price }}</div>
              <div
                class="cursor-pointer text-decoration-underline"
                @click="showOrderDetail(item.id)"
              >
                View details
              </div>
            </div>
          </template>
        </v-data-table>
      </v-tabs-window-item>
    </v-tabs-window>
  </div>
</template>

<script setup>
import { ref, inject, computed, reactive, readonly } from "vue";
import { useRouter } from "vue-router";

const subscripStore = useSubscription();
const { subscriptions } = storeToRefs(subscripStore);

const router = useRouter();
const tab = ref(null);
const headers = ref([
  { title: "Name", key: "name", align: "start" },
  { title: "Delivery date", key: "status", align: "end" },
  { title: "Delivery every", key: "delivery_every", align: "start" },
  { title: "Amount", key: "amount", align: "start" },
]);

const showOrderDetail = (id) => {
  router.push(`/my-account/subscription-details/${id}`);
};

subscripStore.load();
</script>

<style lang="scss" scoped></style>
