<template>
  <div class="pl-8 pr-8">
    <v-tabs v-model="tab" align-tabs="left" color="grey-darken-3">
      <v-tab value="PersonalData">Personal data</v-tab>
      <v-tab value="PasswordChange">Password change</v-tab>
    </v-tabs>
    <v-tabs-window v-model="tab">
      <v-tabs-window-item value="PersonalData" class="pt-9">
        <div class="mb-9">
          Feel free to edit any of your details below so your account is always
          up to date
        </div>
        <div class="px-3 d-flex justify-space-between form-item py-4">
          <span>Email</span>
          <span>{{ data?.email }}</span>
        </div>
        <div class="px-3 d-flex justify-space-between form-item py-4">
          <span>Store Credits</span>
          <span>
            <template v-for="item in data?.store_credits">
              {{ item }}<br />
            </template>
          </span>
        </div>
        <div
          class="px-3 d-flex justify-space-between form-item py-4 align-center"
        >
          <span
            >Shipping to {{ data?.default_shipping_address?.firstname }}
            {{ data?.default_shipping_address?.lastname }}</span
          >
          <v-btn
            height="48"
            variant="tonal"
            @click="router.push(`/my-account/saved-addresses`)"
          >
            Manage saved addresses
          </v-btn>
        </div>
        <div
          class="px-3 d-flex justify-space-between form-item py-4 align-center"
        >
          <span>Orders</span>
          <v-btn
            height="48"
            variant="tonal"
            @click="router.push(`/my-account/order-history`)"
          >
            Show recent orders
          </v-btn>
        </div>
        <div class="mt-9 mb-16">
          At Axel Market, we attach great importance to privacy issues and are
          committed to protecting the personal data of our users. Learn more
          about how we care and use your personal data in the Privacy Policy
        </div>
      </v-tabs-window-item>
      <v-tabs-window-item value="PasswordChange" class="pt-9">
        <v-form @submit.prevent="changePwd">
          <div class="mb-9">
            If you want to change the password to access your account, enter the
            following information:
          </div>
          <v-text-field
            v-model="formData.password"
            color="primary"
            label="New Password *"
            variant="underlined"
            append-inner-icon="mdi-eye-outline"
            :rules="[rules.required]"
          >
          </v-text-field>
          <v-text-field
            v-model="formData.password_confirmation"
            color="primary"
            label="New Password Confirmation *"
            variant="underlined"
            append-inner-icon="mdi-eye-off-outline"
            :rules="[rules.required, rules.comfirmPwd]"
          ></v-text-field>
          <v-btn
            class="mt-4"
            height="48"
            variant="tonal"
            type="submit"
            :disabled="loading"
            v-loading="loading"
          >
            Change password
          </v-btn>
        </v-form>
      </v-tabs-window-item>
    </v-tabs-window>
  </div>
</template>

<script setup>
import { ref, inject, computed, reactive, readonly } from "vue";
import { useRouter, useRoute } from "vue-router";

const router = useRouter();

const { getAccount, getAddresses, resetPwd } = useUser();
const rules = readonly({
  required: (value) => {
    if (!value) {
      return "This field is required";
    }
  },
  comfirmPwd: (value) => {
    if (value == formData.password) {
      return true;
    } else {
      return "The Confirm password is inconsistent with the new password";
    }
  },
});
const loading = ref(null);
const tab = ref(null);
const data = ref({});
const formData = reactive({
  password: "",
  password_confirmation: "",
});

const changePwd = async (evt) => {
  const { valid } = await evt.then();

  if (!valid) {
    return false;
  }
  loading.value = true;
  resetPwd(formData)
    .then((res) => {
      console.log(res);
    })
    .finally(() => {
      loading.value = false;
    });
};

getAccount().then((res) => {
  data.value = res;
});
</script>

<style lang="scss" scoped>
.form-item {
  border-bottom: 2px solid #f1f2f3;
}
.form-item:hover {
  box-shadow: 0px 4px 11px rgba(29, 31, 34, 0.1);
}
</style>
