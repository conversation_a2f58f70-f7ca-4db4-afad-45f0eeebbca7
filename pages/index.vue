<template>
  <template v-for="section in homepageData.sections">
    <AppHeader
      v-if="section.type == 'header' && section.visible"
      :type="'normal'"
      :data="section.properties"
      @open="handleOpen"
    />
    <AnnounceBar
      v-if="section.type == 'announcement_bar' && section.visible"
      :data="section.properties"
    />
    <ImageBanner
      v-if="section.type == 'image_banner' && section.visible"
      :data="section.properties"
    />
    <FeaturedProduct
      v-if="section.type == 'featured_product' && section.visible"
      :data="section.properties"
    />
    <FeaturedArticle
      v-if="section.type == 'featured_article' && section.visible"
      :data="section.properties"
    />
    <Collections
      v-if="section.type == 'collection' && section.visible"
      :data="section.properties"
    />
    <Columns
      v-if="section.type == 'column' && section.visible"
      :data="section.properties"
    />
  </template>
  <AnnounceBar
    v-if="homepageData.announcement_bar_visible"
    :data="homepageData"
  />
  <ImageBanner v-if="homepageData.image_banner_visible" :data="homepageData" />
  <FeaturedProduct
    v-if="homepageData.featured_products_visible"
    :data="homepageData"
  />
  <FeaturedArticle
    v-if="homepageData.featured_article_visible"
    :data="homepageData"
  />
  <Collections v-if="homepageData.collections_visible" :data="homepageData" />
  <Columns v-if="homepageData.columns_visible" :data="homepageData" />
</template>

<script setup>
import { onMounted } from "vue";
import AnnounceBar from "~/components/Homepage/AnnounceBar";
import ImageBanner from "~/components/Homepage/ImageBanner";
import FeaturedProduct from "~/components/Homepage/FeaturedProduct";
import FeaturedArticle from "~/components/Homepage/FeaturedArticle";
import Collections from "~/components/Homepage/Collections";
import Columns from "~/components/Homepage/Columns";

const { loading, load, homepageData } = useHomepage();
const emit = defineEmits(["open"]);
const loadSectionItems = async (options) => {
  load();
};

loadSectionItems();
const handleOpen = (type) => {
  emit("open", type);
};
onMounted(() => {
  loadSectionItems({ client: true });
});
</script>
<style lang="scss"></style>
