<template>
  <div class="my-account mb-8">
    <v-breadcrumbs
      class="desktop-only"
      :items="breadItems"
      divider="|"
    ></v-breadcrumbs>
    <v-toolbar class="smartphone-only pl-2">
      <template v-slot:prepend>
        <v-btn
          v-if="route.name.split('___')[0] != 'my-account'"
          icon="mdi-arrow-left"
          @click="handleBack"
        ></v-btn>
      </template>
      <template v-slot:title>
        <div class="text-center pr-12 mr-5">{{ pageTitle }}</div>
      </template>
    </v-toolbar>
    <div class="content" :class="isPages ? 'sub-page' : 'nav-page'">
      <div class="account-nav pl-5 pt-5 pb-5">
        <div class="text-h5 mb-8">{{ $t("My Account") }}</div>
        <div v-for="(item, i) in navItem" :key="i">
          <div class="text-h6 mb-5">{{ item.title }}</div>
          <div
            class="mb-5 text-grey-darken-3 cursor-pointer nav-text"
            v-for="(nav, i) in item.subTitle"
            :key="i"
            @click="goToPage(nav.pathName, $event)"
          >
            <span>{{ nav.navName }}</span>
            <v-spacer />
            <v-icon icon="mdi-chevron-right"></v-icon>
          </div>
        </div>
      </div>
      <div class="account-page">
        <component :is="currentTabPage" />
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  ref,
  inject,
  computed,
  reactive,
  readonly,
  onMounted,
  watch,
} from "vue";
import MyProfile from "./my-account/my-profile.vue";
import SavedAddressesDetails from "./my-account/saved-addresses.vue";
import OrderHistory from "./my-account/order-history.vue";
import OrderDetails from "./my-account/order-details/[id].vue";
import Subscription from "./my-account/subscription-history.vue";
import SubscriptionDetails from "./my-account/subscription-details/[id].vue";
import { useRouter, useRoute } from "vue-router";

const $notify = inject("notify");
const router = useRouter();
const route = useRoute();
const { logOut } = useUser();

const breadItems = reactive([
  {
    title: "Home",
    disabled: false,
    href: "/",
  },
  {
    title: "My Account",
    disabled: true,
    href: "/my-account/my-profile",
  },
]);
const navItem = reactive([
  {
    title: "Personal Details",
    subTitle: [
      {
        navName: "My profile",
        pathName: "my-profile",
      },
      {
        navName: "Saved addresses",
        pathName: "saved-addresses",
      },
    ],
  },
  {
    title: "Order Details",
    subTitle: [
      {
        navName: "Order history",
        pathName: "order-history",
      },
      {
        navName: "Subscriptions history",
        pathName: "subscription-history",
      },
      {
        navName: "Log out",
        pathName: "home",
      },
    ],
  },
]);
const names = readonly({
  "my-account": "My account",
  "my-account-my-profile": "My profile",
  "my-account-order-history": "Order history",
  "my-account-saved-addresses": "Saved addresses",
  "my-account-subscription-history": "Subscription history",
});
const pageTitle = computed(() => {
  return names[route.name.split("___")[0]];
});
const tab = ref("");
const isMobile = computed(() => {
  return document.body.clientWidth < 1024;
});
const isPages = computed(() => {
  return !route.name.startsWith("my-account___");
});
const currentTabPage = computed(() => {
  const result = route.name.match(/my-account-(.*?)___/);
  let name = tab.value;

  if (result) {
    name = result[1];
  }

  if (route.name.startsWith("my-account-order-details-id")) {
    return OrderDetails;
  } else if (route.name.startsWith("my-account-subscription-details-id")) {
    return SubscriptionDetails;
  } else {
    const pageObj = {
      "my-profile": MyProfile,
      "saved-addresses": SavedAddressesDetails,
      "order-history": OrderHistory,
      "subscription-history": Subscription,
    };
    return pageObj[name];
  }
});

const goToPage = (name, evt) => {
  if (name == "home") {
    logOut().then(() => {
      $notify({
        message: "success",
        type: "Logout successful",
      });
      router.push(`/signin`);
    });
  } else {
    Array.from(document.getElementsByClassName("nav-text")).forEach(
      (element) => {
        element.classList.remove("active");
      },
    );
    evt.target.parentNode.classList.add("active");
    tab.value = name;
    router.push(`/my-account/${name}`);
  }
};

const handleBack = () => {
  const name = route.name.match(/my-account-(.*?)___/)[1];
  if (
    [
      "my-profile",
      "saved-addresses",
      "order-history",
      "subscription-history",
    ].includes(name)
  ) {
    router.push("/my-account");
  } else if (name == "order-details-id") {
    router.push("/my-account/order-history");
  } else if (name == "subscription-details-id") {
    router.push("/my-account/subscription-history");
  } else {
    router.back();
  }
};

const initNav = () => {
  tab.value = route.fullPath.split("/")[2] || "my-profile";
  Array.from(document.getElementsByClassName("nav-text")).forEach((element) => {
    element.classList.remove("active");
    if (
      !isMobile &&
      element.innerText.toLowerCase().indexOf(tab.value.split("-")[0]) > -1
    ) {
      element.classList.add("active");
    }
  });
};

onMounted(() => {
  initNav();
});

watch(
  () => route.fullPath,
  (val) => {
    if (val) {
      initNav();
    }
  },
);
</script>

<style lang="scss" scoped>
@use "~/assets/styles/default" as *;
.my-account {
  max-width: 1100px;
  margin: 0 auto;
  .content {
    display: flex;
    border-bottom: 1px solid #f1f2f3;
    .account-nav {
      width: 280px;
      background: #f1f2f3;
    }
    .nav-text {
      display: flex;
      margin-right: 12px;
      .v-icon {
        display: none;
      }
    }
  }
  @include for-mobile {
    .content {
      display: block;
    }
    .nav-page {
      .account-nav {
        display: block;
        width: 100%;
      }
      .account-page {
        display: none;
      }
    }
    .sub-page {
      .account-nav {
        display: none;
      }
      .account-page {
        display: block;
        width: 100%;
      }
    }
    .nav-text {
      .v-icon {
        display: block !important;
      }
    }
  }
}
.account-page {
  width: calc(100% - 280px);
}
.active {
  color: #5ece7b !important;
}
</style>
