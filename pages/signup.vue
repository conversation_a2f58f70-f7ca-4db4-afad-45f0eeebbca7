<template>
  <div class="signup">
    <v-form v-if="step == 1" fast-fail @submit.prevent="handleSendCode">
      <h2 class="py-8">Create account</h2>
      <v-text-field
        v-model="formData.email"
        class="mb-2"
        :label="$t('Your email')"
        prepend-inner-icon="mdi-at"
        variant="outlined"
        clearable
        :rules="[rules.required]"
      ></v-text-field>
      <!--      <v-text-field-->
      <!--        v-model="formData.phone"-->
      <!--        class="mb-2"-->
      <!--        :label="$t('Phone number')"-->
      <!--        prepend-inner-icon="mdi-cellphone"-->
      <!--        variant="outlined"-->
      <!--        clearable-->
      <!--        :rules="[rules.required]"-->
      <!--      ></v-text-field>-->
      <!--      <v-text-field-->
      <!--        v-model="formData.full_name"-->
      <!--        :rules="[rules.required]"-->
      <!--        prepend-inner-icon="mdi-account"-->
      <!--        class="mb-2"-->
      <!--        :label="$t('Full Name')"-->
      <!--        variant="outlined"-->
      <!--        clearable-->
      <!--      ></v-text-field>-->
      <v-text-field
        v-model="formData.password"
        :rules="[rules.required]"
        prepend-inner-icon="mdi-lock-outline"
        :append-inner-icon="pwdType ? 'mdi-eye' : 'mdi-eye-off'"
        :type="pwdType ? 'text' : 'password'"
        @click:append-inner="pwdType = !pwdType"
        class="mb-2"
        :label="$t('Create Password')"
        variant="outlined"
        clearable
      ></v-text-field>
      <v-btn
        class="mt-2"
        color="black"
        size="large"
        rounded="0"
        type="submit"
        block
        :disabled="loading"
        :loading="loading"
        >{{ $t("Sign up") }}</v-btn
      >
      <div class="my-6">
        <span>Already have an account? </span>
        <nuxt-link-locale to="/signin">
          <b class="text-decoration-underline text-uppercase ml-10">{{
            $t("Sign in")
          }}</b>
        </nuxt-link-locale>
      </div>
    </v-form>
    <v-form v-if="step == 2">
      <h2 class="text-center">
        Enter the 6-digit code that is sent to your email
      </h2>
      <p class="my-8">
        This helps us keep your account secure by verifying that it's really
        you.
      </p>
      <v-otp-input
        v-model="formData.code"
        type="number"
        size="large"
        @finish="handleVerify"
      ></v-otp-input>
      <div class="d-flex mt-8">
        <span class="text-decoration-underline" @click="handleBack">BACK</span>
        <v-spacer />
        <span>Didn’t receive a code? </span>
        <span class="ml-2 font-weight-black text-primary" @click="handleResend"
          >RESEND</span
        >
      </div>
    </v-form>
    <div v-if="step == '3'">
      <v-empty-state icon="$success">
        <template v-slot:media>
          <v-icon color="surface-variant"></v-icon>
        </template>

        <template v-slot:headline>
          <div class="text-h4">All Done For Now!</div>
        </template>

        <template v-slot:title>
          <div class="text-h6">Your account has been created.</div>
        </template>

        <template v-slot:actions>
          <v-btn
            class="text-none"
            color="white"
            elevation="1"
            rounded="lg"
            size="small"
            text="Login"
            width="96"
            @click="gotoSignin"
          ></v-btn>

          <v-btn
            class="text-none"
            elevation="1"
            rounded="lg"
            size="small"
            text="Home"
            width="96"
            @click="gotoHome"
          ></v-btn>
        </template>
      </v-empty-state>
    </div>
  </div>
</template>

<script setup>
import { reactive, inject, readonly, ref } from "vue";
import { useRouter } from "vue-router";

const { sendCode, resendCode, verifyCode, signUp } = useUser();
const $notify = inject("notify");

const router = useRouter();
const rules = readonly({
  required: (value) => {
    if (!value) {
      return "This field is required";
    }
  },
});
const loading = ref(false);
const step = ref(1);
const pwdType = ref(true);
const formData = reactive({
  email: "",
  phone: "",
  full_name: "",
  password: "",
  code: "",
});
const handleBack = () => {
  step.value = 1;
};
const handleSendCode = async (evt) => {
  const { valid } = await evt.then();

  if (!valid) {
    return false;
  }
  loading.value = true;
  sendCode({
    email: formData.email,
    commit: "sign_up",
  })
    .then((res) => {
      $notify({
        message: "success",
        type: "The verification code has been sent to your email",
      });
      step.value = 2;
    })
    .finally(() => {
      loading.value = false;
    });
};
const handleResend = () => {
  resendCode({
    email: formData.email,
    confirmation_for: "seller_sign_up",
  })
    .then((res) => {
      console.log(res);
      $notify({
        message: "success",
        type: "The verification code has been sent to your email",
      });
    })
    .finally(() => {
      loading.value = false;
    });
};
const handleVerify = () => {
  verifyCode({
    email: formData.email,
    confirmation_code: formData.code,
  })
    .then((res) => {
      handleSignup({
        token: res.token,
        password: formData.password,
        password_confirmation: formData.password,
      });
    })
    .finally(() => {
      loading.value = false;
      step.value = 2;
    });
};
const handleSignup = async (params) => {
  signUp(params)
    .then((res) => {
      step.value = 3;
    })
    .finally(() => {
      loading.value = false;
    });
};
const gotoSignin = () => {
  router.push("/signin");
};
const gotoHome = () => {
  router.push("/");
};
</script>

<style lang="scss">
.signup {
  max-width: 560px;
  min-height: 450px;
  margin: 84px auto;
  padding: 0 16px;
}
</style>
