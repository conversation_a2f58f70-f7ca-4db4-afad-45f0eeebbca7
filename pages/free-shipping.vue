<template>
  <div id="freeShipping" class="main free-shipping">
    <div class="free-shipping__content-box">
      <div class="free-shipping__title">
        <p
          class="text-h6 font-weight-bold mb-4"
          v-html="$t('Welcome To Our We Pay For Your Shipping Program')"
        ></p>
        <p
          class="font-size-16"
          v-html="
            $t(
              'Get Free Shipping, Accurate Tracking, Cool Discounts, & Other Goodies',
            )
          "
        ></p>
      </div>
      <div class="free-shipping__text-box">
        <div class="left text-box">
          <ul class="desc-ul">
            <li>
              <v-icon icon="credits" />
              <p v-html="$t('No minimum order')"></p>
            </li>
            <li>
              <SfIcon icon="shipping" size="40px" />
              <p v-html="$t('We PAY FOR YOUR SHIPPING')"></p>
            </li>
            <li>
              <SfIcon icon="gift" size="40px" />
              <p v-html="$t('Your order will ship by')"></p>
            </li>
            <li>
              <SfIcon icon="arrow_right" size="36px" />
              <p v-html="$t('Some restrictions apply')"></p>
            </li>
          </ul>
        </div>
        <div class="right text-box">
          <h4 v-html="$t('Exclusions')"></h4>
          <ul class="policy-list">
            <li v-html="$t('Hawaii & Alaska')"></li>
            <li v-html="$t('Wholesale Customers')"></li>
            <li
              v-html="
                $t(
                  'Large quantities, however, you can still place your order online',
                )
              "
            ></li>
            <li v-html="$t('Select items are not available')"></li>
            <li v-html="$t('Occasionally, items temporarily')"></li>
            <li v-html="$t('International customers are responsible')"></li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup></script>

<style lang="scss" scoped>
.free-shipping {
  display: flex;
  justify-content: center;
  &__title {
    text-align: center;
  }
  &__content-box {
    min-height: 800px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 80%;
  }
  &__text-box {
    .left {
      padding: 0 10px;
      .desc-ul {
        list-style: none;
        padding-left: 0;
        > li {
          display: flex;
          align-items: center;
          font-size: 13px;
          line-height: 28px;
          padding: 20px;
          .sf-icon {
            margin-right: 10px;
            &:hover {
              text-shadow: none;
            }
          }
          &:not(:last-child) {
            border-bottom: 2px dotted #84aac5;
          }
        }
      }
    }
    .right {
      background-color: #fff;
      padding: 40px;
      .policy-list {
        padding-left: 15px;
        font-size: 12px;
        color: #515151;
        opacity: 0.8;
        font-style: italic;
        line-height: 25px;
      }
    }
    .text-box {
      box-sizing: border-box;
    }
  }
}
</style>
