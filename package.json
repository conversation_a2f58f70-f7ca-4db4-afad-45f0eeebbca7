{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build --dotenv .env.production", "build:production": "nuxt build --dotenv .env.production", "dev": "nuxt dev --dotenv .env.development", "generate": "nuxt generate", "preview": "nuxt preview --dotenv .env.production", "postinstall": "nuxt prepare", "format": "prettier --write pages/"}, "dependencies": {"@mdi/font": "^7.4.47", "@nuxt/image": "^1.10.0", "@nuxtjs/i18n": "^9.4.0", "@nuxtjs/robots": "^5.2.8", "@nuxtjs/sitemap": "^7.2.9", "@nuxtjs/vuetify": "^1.12.3", "@pinia/nuxt": "^0.10.1", "@types/node": "^22.14.1", "js-cookie": "^3.0.5", "notiwind": "^2.1.0", "nuxt": "^3.16.1", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.2.0", "sass": "^1.86.1", "vite-plugin-vuetify": "^2.1.0", "vue": "^3.5.13", "vuetify": "^3.7.19"}, "devDependencies": {"prettier": "3.5.3", "sass-embedded": "^1.86.0"}}