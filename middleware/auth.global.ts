export default defineNuxtRouteMiddleware((to, from) => {
    const token = useCookie('token');
    const locale = useCookie('locale') || 'en';
    const name = to.name ? to.name.split('___') : [];

    if (to.name && to.name.startsWith('my-account') && !token) {
        return navigateTo({
            name: 'signin___' + (locale.value || 'en')
        })
    }
    console.log(locale, 'locale')

    // Only handle locale routing if we have a valid route name
    if (name.length > 1 && name[1] == (locale.value || 'en')) {
        return true
    } else if (name.length > 0 && name[0]) {
        return navigateTo({
            name: name[0] + '___' + (locale.value || 'en')
        })
    }

    return true
});
