export default defineNuxtRouteMiddleware((to, from) => {
    const token = useCookie('token');
    const locale = useCookie('locale') || 'en';
    const name = to.name.split('___');

    if (to.name.startsWith('my-account') && !token) {
        return navigateTo({
            name: 'signin___' + locale.value
        })
    }
    console.log(locale, 'locale')

    if (name[1] == locale.value) {
        return true
    } else {
        return navigateTo({
            name: name[0] + '___' + locale.value
        })
    }
});
