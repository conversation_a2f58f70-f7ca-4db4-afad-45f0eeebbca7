ARG NODE_VERSION=18.20.8
FROM node:${NODE_VERSION}-alpine as builder

ARG COMMIT_SHA
ARG COMMIT_REF_NAME
ARG COMMIT_DATE
ARG COMMIT_MESSAGE
ARG PROJECT_URL
ARG DEPLOY_DATE
ARG DEPLOY_USER

ARG NPM_REGISTRY
ARG REGISTRY_PULL_USER
ARG REGISTRY_PULL_PASSWORD
ARG ID_RSA

RUN apk add --no-cache openssh-client git

RUN mkdir /root/.ssh/ && \
    echo "$ID_RSA" > /root/.ssh/id_rsa && \
    chmod 600 /root/.ssh/id_rsa && \
    touch /root/.ssh/known_hosts && \
    ssh-keyscan gitlab.axel.org > /root/.ssh/known_hosts

WORKDIR /build

ADD . .

RUN npm install -g yarn --force && \
    yarn install && \
    yarn run build
RUN echo -n "{\"projectUrl\": \"${PROJECT_URL}\", \"commitSha\": \"${COMMIT_SHA}\", \"commitTag\": \"${COMMIT_REF_NAME}\", \"commitMessage\": " > version.json && \
    printf '%s' "$COMMIT_MESSAGE" | \
    node -e 'var stdin = process.openStdin();var data = "";stdin.on("data", function(chunk) {data += chunk;});stdin.on("end", function() {console.log(JSON.stringify(data));});' >> version.json && \
    echo -n ", \"commitDate\": \"" >> version.json && \
    git show -s --format=%cI | \
    xargs echo -n | \
    node -e "var stdin = process.openStdin();var data = '';stdin.on('data', function(chunk) {data += chunk;});stdin.on('end', function() {console.log(new Date(data).toISOString())});" | \
    xargs echo -n >> version.json && \
    echo -n "\", " >> version.json

FROM node:${NODE_VERSION}-alpine

MAINTAINER Petro Khodko "<EMAIL>"

WORKDIR /project

COPY --from=builder /build/.output /project/.output
COPY --from=builder /build/version.json /project/version.json

ARG COMMIT_SHA
ARG COMMIT_REF_NAME
ARG COMMIT_MESSAGE
ARG COMMIT_DATE
ARG PROJECT_URL

ENV COMMIT_SHA $COMMIT_SHA
ENV COMMIT_REF_NAME $COMMIT_REF_NAME
ENV COMMIT_MESSAGE $COMMIT_MESSAGE
ENV COMMIT_DATE $COMMIT_DATE
ENV PROJECT_URL $PROJECT_URL

RUN touch entrypoint.sh && \
    chmod +x entrypoint.sh && \
    echo "#!/bin/sh" > entrypoint.sh && \
    echo "echo -n \"\\\"deployDate\\\": \\\"\${DEPLOY_DATE}\\\", \\\"deployUser\\\": \\\"\${DEPLOY_USER}\\\"}\" >> version.json" >> entrypoint.sh && \
    echo "node .output/server/index.mjs" >> entrypoint.sh

ENTRYPOINT ["./entrypoint.sh"]

EXPOSE 5000/tcp
