export default {
  ssr: true,
  target: 'server',
  devServer: {
    port: 5000, // 指定端口为 5000
  },
  app: {
    head: {
      title: 'Axel Marketplace',
      meta: [
        { charset: 'utf-8'},
        { name: 'description', content: 'market' }
      ],
      link: [
          {rel: 'icon', type: 'image/x-icon', href: '/favicon.ico'}
      ]
    }
  },
  css: [
    'vuetify/styles/main.sass',
    '@mdi/font/css/materialdesignicons.min.css',
    '~/assets/styles/default.scss',
    '~/assets/styles/notification.scss'
  ],
  vite: {
    css: {
      resolve: {
        extensions: ['.css']
      }
    },
    server: {
      proxy: {
        '/api': {
          target: 'https://newmarket.viacube.com',
          changeOrigin: true
        }
      }
    }
  },
  //Unknown file extension ".css"
  build: {
    transpile: [
      'vuetify',
      /vuetify.*/
    ]
  },
  runtimeConfig: {
    public: {
      env: process.env.NODE_ENV,
      targetUrl: process.env.NUXT_PUBLIC_API_TARGET,
      apiBaseUrl: process.env.NUXT_PUBLIC_API_BASE_URL
    }
  },
  modules: [
    '@nuxtjs/sitemap',
    '@nuxtjs/robots',
    '@nuxtjs/i18n',
    '@pinia/nuxt'
  ],

  i18n: {
    strategy: 'prefix_except_default',
    locales: [
      {code: 'en', iso: 'en-US', label: 'English'},
      {code: 'es',iso: 'es-MX', label: 'Español'},
      {code: 'pt', iso: 'pt-BR', label: 'Português'}
    ],
    lazy: true,
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'locale',
      fallbackLocale: 'en'
    },
    defaultLocale: 'en',
    vueI18n: '~/i18n.config.ts'
  },
  pinia: {
    autoImports: ["defineStore"]
  },

  sitemap: {
  },

  robots: {
    UserAgent: '*',
    Allow: '/'
  },

  generate: {
  },

  compatibilityDate: '2025-03-31'
};
